{"name": "gov-forest-flow-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --port 3000 --open", "dev": "NODE_ENV=dev vite --host 0.0.0.0 --port 3000", "build": "NODE_ENV=dev tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@ag-grid-community/client-side-row-model": "^32.3.3", "@ag-grid-community/core": "^32.3.3", "@ag-grid-community/infinite-row-model": "^32.3.3", "@ag-grid-community/react": "^32.3.3", "@ag-grid-community/styles": "^33.0.3", "@hello-pangea/dnd": "^17.0.0", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-code-block-lowlight": "^2.11.5", "@tiptap/extension-collaboration": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-focus": "^2.11.5", "@tiptap/extension-font-family": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-typography": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/extension-youtube": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tosspayments/tosspayments-sdk": "^2.3.4", "@types/react-router-dom": "^5.3.3", "ag-grid-community": "^33.2.1", "axios": "^1.7.8", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "echarts-for-react": "^3.0.3", "env-cmd": "^10.1.0", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "mitt": "^3.0.1", "number-format.js": "^2.0.9", "qrcode-generator": "^1.4.4", "qs": "^6.14.0", "react": "^18.3.1", "react-checkbox-tree": "^1.8.0", "react-datepicker": "^8.1.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-kakao-maps-sdk": "^1.1.27", "react-router-dom": "^7.0.2", "react-spinners": "^0.15.0", "react-tooltip": "^5.28.0", "sass": "^1.81.1", "swiper": "^11.2.6", "tiptap-extension-font-size": "^1.2.0", "vite-plugin-env-compatible": "^2.0.1", "zustand": "^5.0.1"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@eslint/js": "^9.15.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@types/lodash": "^4.17.16", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/browser": "^3.1.1", "@vitest/coverage-v8": "^3.1.1", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-storybook": "^0.12.0", "globals": "^15.12.0", "postcss": "^8.4.49", "prettier": "^3.4.1", "storybook": "^8.6.12", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1", "vite-tsconfig-paths": "^5.1.3", "vitest": "^3.1.1"}, "resolutions": {"@types/react": "YOUR_CURRENT_REACT_VERSION", "@types/react-dom": "YOUR_CURRENT_REACT_VERSION"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}