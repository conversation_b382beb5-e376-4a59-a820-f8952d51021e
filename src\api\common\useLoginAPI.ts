import axios from 'axios';
import { SessionData, useNavigate } from 'react-router-dom';
import { useAlertStore } from '@store/useAlertStore';
import { useEventStore } from '@store/useEventStore';
import { getEventPopupNow } from '@api/admin/eventPopupAPI';

const useLoginAPI = () => {
  const navigate = useNavigate();
  const { activeAlert } = useAlertStore();

  const handlePostLogin = async (postData, isRememberId, setIsLogin) => {
    const headers = {
      'X-Requested-With': 'XMLHttpRequest',
    };

    await axios
      .post('/auth/admin-tokens', postData, { headers })
      .then((response) => {
        if (response.status === 201 && response.data.code === 'SUCCESS') {
          // 로그인 성공
          const authorization = response.headers['authorization'];
          const accessToken = authorization?.replace(/^Bearer\s/, '') || '';

          if (accessToken) {
            localStorage.setItem('accessToken', accessToken);

            if (isRememberId) {
              localStorage.setItem('loginUsername', postData?.username); // 아이디 저장하기 체크되어 있는 경우
            } else if (localStorage.getItem('loginUsername')) {
              localStorage.removeItem('loginUsername'); // 아이디 저장하기 체크 안되어있는데 스토리지에 남아있는 경우
            }

            navigate('/');
            setIsLogin(true);
          } else {
            // 로그인 실패 alert
            activeAlert('로그인에 실패하였습니다');
            setIsLogin(false);
          }
        }
      })
      .catch((error) => {
        // 로그인 실패 alert (request 4xx)
        activeAlert(error.response.data.message);
      });
  };

  const handlePostSessionData = async (
    setSessionData: (data: SessionData) => void,
    setRemainingTime: (time: number) => void
  ) => {
    try {
      await axios.post('/api-qrLogin').then((response) => {
        const result = response.data;
        setSessionData(result.data);
      });
    } catch (error) {
      activeAlert(error.response.data.message);
    }
  };

  const connectSocket = (
    sessionId: string,
    handleCloseModal: () => void,
    setRemainingTime: (time: number) => void,
    setIsExpired: (isExpired: boolean) => void
  ) => {
    const socket = new WebSocket(`/api-ws/qr?sessionId=${sessionId}`);
    socket.onopen = () => {
      console.log('WebSocket connected');
    };

    socket.onmessage = (event) => {
      console.log('WebSocket message received:', JSON.parse(event.data));
      const parseData = JSON.parse(event.data);
      if (parseData.type === 'verifyQrLogin') {
        socket.close();
        localStorage.setItem('accessToken', parseData.token);
        navigate('/monitoring');
      } else if (parseData.type === 'timeRemaining') {
        setRemainingTime(parseData.message);
      } else if (parseData.type === 'sessionExpired') {
        setIsExpired(true);
      }
    };

    socket.onclose = () => {
      console.log('WebSocket closed');
    };
    socket.onerror = (error) => {
      activeAlert('소켓 연결 실패', handleCloseModal);
      console.error('WebSocket error:', error);
    };
    return socket;
  };

  return {
    handlePostLogin,
    handlePostSessionData,
    connectSocket,
  };
};

export default useLoginAPI;
