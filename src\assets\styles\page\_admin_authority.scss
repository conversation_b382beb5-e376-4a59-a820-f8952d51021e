@use '@styles/utils/mixin' as m;

.manage_admin_authority {
  min-height: calc(100vh - 110px);

  .content {
    .left_content,
    .right_content {
      .c_list_table_contents {
        > div {
          justify-content: space-between;
        }
      }

      .admin_authority_detail_info_wrapper {
        position: relative;
        width: 100%;
        margin-bottom: 0.625rem;

        .no_result {
          @include m.flex(center, center);
          width: 100%;
          height: calc(100vh - 330px);
          border-radius: 0.5rem;
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--g_06);

          @include m.bp_large() {
            height: calc(100vh - 300px);
          }

          @include m.bp_medium() {
            height: calc(100vh - 270px);
          }
        }
      }
    }

    .right_content {
      .authority_list {
        width: 100%;
        gap: 1rem;

        > div {
          width: 50%;
        }
      }

      //메뉴 권한
      .menu_auth_contents {
        height: calc(100vh - 570px);
        padding: 0.5rem;
        border: 1px solid var(--g_04);
        border-radius: 0.5rem;
        overflow-y: auto;

        ol {
          @include m.flex(start, center, column);
          gap: 0.5rem;
        }

        .depth1_menu {
          @include m.flex(start, center, column);
          gap: 0.5rem;

          ol {
            .depth2_menu {
              @include m.flex(start, center, column);
              gap: 0.5rem;

              label {
                background-color: var(--g_01);
              }

              ol {
                .depth3_menu {
                  label {
                    background-color: white;
                  }
                }
              }
            }
          }

          label {
            @include m.flex(center, start);

            min-width: 250px;
            padding: 0.25rem;
            background-color: var(--g_02);
            border-radius: 0.5rem;

            font-size: 0.875rem;

            @include m.bp_medium() {
              font-size: 0.75rem;
            }
          }

          .rct-collapse-btn {
            .rct-icon-expand-open {
              &::before {
                content: '';
                background-image: url('@assets/images/icon/icon_triangle_up.svg');
                display: inline-block;
                width: 1rem;
                height: 1rem;
                background-size: contain;
                background-repeat: no-repeat;
                transform: rotate(180deg);
              }
            }

            .rct-icon-expand-close {
              &::before {
                content: '';
                background-image: url('@assets/images/icon/icon_triangle_up.svg');
                display: inline-block;
                width: 1rem;
                height: 1rem;
                background-size: contain;
                background-repeat: no-repeat;
                transform: rotate(90deg);
              }
            }
          }

          .rct-checkbox {
            @include m.flex(center, center);

            width: 24px;
            height: 24px;

            .rct-icon-uncheck,
            .rct-icon-half-check {
              position: relative;

              &::before {
                content: '';
                display: inline-block;
                width: 16px;
                height: 16px;
                background-image: url('@assets/images/icon/icon_checkbox_disabled.svg');
                background-size: contain;
                background-repeat: no-repeat;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              }
            }

            .rct-icon-check {
              position: relative;

              &::before {
                content: '';
                display: inline-block;
                width: 16px;
                height: 16px;
                background-image: url('@assets/images/icon/icon_checkbox.svg');
                background-size: contain;
                background-repeat: no-repeat;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              }
            }
          }

          .rct-node-icon {
            display: none;
          }
        }
      }
      
      // 서비스 권한
      .service_auth_contents {
        @include m.flex(center, center, row);

        width: 100%;
        gap: 0.5rem;

        .c_grid_container {
          flex: 1;
          height: calc(100vh - 570px);

          .selected {
            background-color: var(--p_01);
          }

          .ag-theme-quartz .ag-row.ag-row-selected:not(.selected):not(:hover),
          .ag-theme-quartz .ag-row.ag-row-selected:not(.selected):not(:hover) .ag-cell {
            background-color: white !important;
          }

          .ag-header-viewport {
            @include m.flex(center, center, row);
          }

          .ag-header-cell,
          .ag-cell {
            &:last-child {
              border-left: 1px solid var(--g_04);
            }
          }

          .ag-center-cols-viewport {
            .ag-center-cols-container {
              .ag-cell {
                @include m.flex(center, start);
              }
            }
          }
        }
      }

      // 페이지 권한
      .page_auth_contents {
        height: calc(100vh - 570px);

        .c_grid_container {
          height: 100%;

          .ag-cell-wrapper.ag-checkbox-cell {
            @include m.flex(center, center);
          }
        }
      }

      @include m.bp_large() {
        .menu_auth_contents,
        .service_auth_contents,
        .page_auth_contents {
          height: calc(100vh - 540px);
        }
      }

      @include m.bp_medium() {
        .menu_auth_contents,
        .service_auth_contents,
        .page_auth_contents {
          height: calc(100vh - 500px);
        }
      }
    }
  }
}
