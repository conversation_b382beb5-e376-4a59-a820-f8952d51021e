import React, { ChangeEvent, DragEvent, HTMLAttributes, useRef, useState } from 'react';

import joinClassName from '@utils/joinClassName';
import { useAlertStore } from '@store/useAlertStore';
import FileSingle from '@components/common/Filebox/components/FileSingle';
import FileMulti from '@components/common/Filebox/components/FileMulti';
import FileMultiImage from '@components/common/Filebox/components/FileMultiImage';

// Response File 타입
export interface ResponseFile {
  id: string;
  path: string;
  fullPath: string;
  name: string;
  originName: string;
  size: number;
  extension: string;
  delYn: string;
  dateInfo: {
    createUser: string | null;
    createDate: string | null;
    updateUser: string | null;
    updateDate: string | null;
  };
}

// type 속성을 제외한 HTMLInputElement의 모든 HTML 속성을 상속받습니다
interface FileboxProps extends Omit<HTMLAttributes<HTMLInputElement>, 'type'> {
  files: FileList | null;
  setFiles: (file: FileList | null) => void;
  imageList?: ResponseFile[] | null;
  setImageList?: (imageList: ResponseFile[] | null) => void;
  maxFileSize?: number; // MB 단위
  multiple?: boolean; // 다중파일 업로드 여부
  type?: 'image' | 'file';
  accept?: string[]; // 파일 확장자 문자열 (예: 'jpg,png,gif')
  name?: string;
  className?: string;
  readOnly?: boolean;
}

const Filebox = ({
  files,
  setFiles,
  imageList,
  setImageList,
  maxFileSize = 10,
  multiple = false,
  type = 'file',
  accept = ['jpg', 'jpeg', 'png', 'gif'],
  name = '',
  className,
  readOnly = false,
}: FileboxProps) => {
  const ref = useRef<HTMLInputElement>(null);
  const { activeAlert } = useAlertStore();
  const [isActive, setIsActive] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);

  // 파일 초기화 로직
  const initFileList = () => {
    ref.current.value = '';
    ref.current.files = null;
    setFiles(null);
  };

  // 파일 삭제 로직
  const handleDelete = (e: React.MouseEvent<HTMLButtonElement>, file: File) => {
    if (readOnly) return;
    e.preventDefault();
    e.stopPropagation();
    // 삭제할 파일 제외 파일 목록 생성
    // 파일의 이름과 크기가 같은 파일을 제외하고 남은 파일 목록을 생성
    const filteredFiles = Array.from(files).filter(
      (fileItem) => !(fileItem.name === file.name && fileItem.size === file.size)
    );

    // 삭제할 파일 제외 파일 목록을 데이터 전송 객체에 추가
    const dataTransfer = new DataTransfer();
    filteredFiles.forEach((file) => dataTransfer.items.add(file));

    // 삭제할 파일 제외 파일 목록을 입력 요소에 저장
    ref.current.files = dataTransfer.files;

    // 삭제할 파일 제외 파일 목록을 상태에 저장
    setFiles(dataTransfer.files);
  };

  const handleAddFiles = (fileList: File[]) => {
    if (readOnly) return;
    const dataTransfer = new DataTransfer();
    fileList.forEach((file) => dataTransfer.items.add(file));
    ref.current.files = dataTransfer.files;
    setFiles(dataTransfer.files);
  };

  const handleAddClick = () => {
    if (readOnly) return;
    ref.current?.click();
  };

  const isValidAndSetFiles = (fileList: FileList | null) => {
    // 파일 없음
    if (!fileList?.length) {
      return false;
    }

    // multiple = false 일 때, 다중 파일 업로드 불가
    if (!multiple && fileList.length > 1) {
      activeAlert('하나 이상의 파일은 업로드 할 수 없습니다.');
      return false;
    }

    const validFiles: File[] = [];
    const invalidFiles: File[] = [];

    // 파일 용량 및 타입 체크
    Array.from(fileList).forEach((file) => {
      let isValid = true;

      // 파일 크기 체크 (MB 단위로 변환)
      if (maxFileSize && file.size > maxFileSize * 1024 * 1024) {
        isValid = false;
      }

      // 파일 타입 체크 - 확장자로 검사
      if (accept) {
        const acceptType = accept.map((type) => file.name.toLowerCase().endsWith(`.${type.toLowerCase()}`));
        const validResult = acceptType.some((type) => type === true);
        if (!validResult) {
          isValid = false;
        }
      }

      return isValid ? validFiles.push(file) : invalidFiles.push(file);
    });

    // 유효하지 않은 파일이 있는 경우 알림
    if (invalidFiles.length > 0) {
      activeAlert(
        <>
          {invalidFiles.map((file) => (
            <b>
              {file.name} <br />
            </b>
          ))}
          <br />
          파일이 업로드 되지 않았습니다.
          <br />
          파일 크기는 최대 <b>{maxFileSize}MB</b>까지이며,
          <br />
          허용하는 파일타입은 <b>" {accept.join(', ')} "</b> 입니다.
        </>
      );
    }

    // 유효한 파일이 없는 경우
    if (validFiles.length === 0) {
      return false;
    }

    // 파일 목록 저장 multiple = true 일 때는 기존 파일 목록에 더하는 방식으로 추가
    if (multiple) {
      const prevFiles = files ? Array.from(files) : [];

      // 중복 파일 필터링
      const uniqueFiles = validFiles.filter(
        (newFile) => !prevFiles.some((prevFile) => prevFile.name === newFile.name && prevFile.size === newFile.size)
      );

      // 중복 파일 제거 후 기존 파일 목록에 추가
      const combinedFiles = [...prevFiles, ...uniqueFiles];
      handleAddFiles(combinedFiles);
    } else {
      handleAddFiles(validFiles);
    }

    return true;
  };

  // 클릭 후 파일 선택 시
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    console.log('등록!!');
    const targetFiles = e.target.files;
    isValidAndSetFiles(targetFiles);
  };

  // 공통 - 드래그 이벤트 방지
  const preventDragEvent = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // dropArea 위에 입장 할 때
  const handleDragStart = (e: DragEvent<HTMLDivElement>) => {
    if (readOnly) return;
    preventDragEvent(e);
    setDragCounter((prev) => prev + 1);
    setIsActive(true);
  };

  // dropArea 위에서 벗어났을때
  const handleDragEnd = (e: DragEvent<HTMLDivElement>) => {
    if (readOnly) return;
    preventDragEvent(e);
    setDragCounter((prev) => prev - 1);
    if (dragCounter <= 1) {
      setIsActive(false);
      setDragCounter(0);
    }
  };

  // dropArea 위에 있을 때
  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    if (readOnly) return;
    preventDragEvent(e);
    setIsActive(true);
  };

  // dropArea 위에 파일 드랍 시
  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    if (readOnly) return;
    preventDragEvent(e);
    setIsActive(false);

    const droppedFiles = e.dataTransfer?.files;
    isValidAndSetFiles(droppedFiles);
  };

  // 파일 드래그시 클래스 추가 여부
  const activeClass = isActive ? 'active' : '';

  return (
    <div
      className={joinClassName('c_filebox', className)}
      onDragEnter={handleDragStart}
      onDragLeave={handleDragEnd}
      onDragOver={handleDragOver}
      onDrop={(e) => {
        handleDrop(e);
        setDragCounter(0);
      }}
    >
      <label className={joinClassName('c_filebox_area', activeClass)}>
        <input type="file" ref={ref} onChange={handleChange} multiple={multiple} name={name} />
        {!multiple && type === 'file' && (
          <>
            {!files || files?.length === 0 ? (
              <div className="c_filebox_drop_area">
                <div className="c_filebox_drop_area_desc">
                  <figure className="c_filebox_drop_area_icon" />
                  {!isActive ? (
                    <>
                      <p className="c_filebox_drop_area_text">클릭 혹은 파일을 이곳에 드랍해주세요.</p>
                      <p className="c_filebox_drop_area_text">
                        파일 크기는 최대 {maxFileSize}MB까지 업로드 가능합니다.
                      </p>
                    </>
                  ) : (
                    <p className="c_filebox_drop_area_text">파일을 드랍해주세요.</p>
                  )}
                </div>
              </div>
            ) : (
              <div className="c_filebox_file_area">
                <FileSingle file={files[0]} onDelete={initFileList} />
              </div>
            )}
          </>
        )}
      </label>
      {multiple && type === 'file' && (
        <FileMulti files={files} onDelete={handleDelete} handleAddClick={handleAddClick} activeClass={activeClass} />
      )}
      {type === 'image' && (
        <FileMultiImage
          files={files}
          imageList={imageList}
          setImageList={setImageList}
          onDelete={handleDelete}
          handleAddClick={handleAddClick}
          activeClass={activeClass}
          readOnly={readOnly}
        />
      )}
    </div>
  );
};

export default Filebox;
