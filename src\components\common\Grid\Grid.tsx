import React, { forwardRef, useCallback } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model';
import {
  ColDef,
  ColGroupDef,
  GridOptions,
  GridSizeChangedEvent,
  ModuleRegistry,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from '@ag-grid-community/core';
import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-quartz.css';
import joinClassName from '@utils/joinClassName';
import GridLoading from '@components/common/Grid/GridLoading';

ModuleRegistry.registerModules([InfiniteRowModelModule, ClientSideRowModelModule]);

interface Props {
  isLoading?: boolean; // 그리드 로딩 상태
  ref: React.MutableRefObject<any>; // ref
  columns: (ColDef | ColGroupDef)[] | null; // 컬럼
  rowData: any[]; // 그리드 데이터
  autoSizeStrategy?:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy
    | 'onGridSizeChanged'; // 셀 넓이 타입
  defaultColDef?: ColDef; // 기본 컬럼 설정
  headerTextAlign?: 'center' | 'left' | 'right'; // 헤더 텍스트 정렬
  movable?: boolean; // 컬럼 이동 가능 여부 (true: 가능 / false: 불가능)
  sortable?: boolean; // 컬럼 sorting 가능 여부 (true: 가능 / false: 불가능)
  resizable?: boolean; // 컬럼 resize 가능 여부 (true: 가능 / false: 불가능)
  gridOptions?: GridOptions; // 기타 그리드 옵션
  placeholder?: string; // 데이터 없을 시 안내 문구
  sizeReset?: boolean; // 기본 사이즈 해제(커스텀 시)
  singleClickEdit?: boolean; // 클릭 시 수정 가능 여부 (true: 가능 / false: 불가능)
}

const Grid = forwardRef<any, Props>(
  (
    {
      isLoading = false,
      columns,
      rowData,
      autoSizeStrategy = { type: 'fitGridWidth' },
      defaultColDef,
      headerTextAlign = 'center',
      movable = false,
      sortable = false,
      resizable = false,
      gridOptions,
      placeholder = '조회된 데이터가 없습니다.',
      sizeReset = false,
      singleClickEdit = true,
    },
    ref
  ) => {
    const containerClass = joinClassName('c_grid_container', sizeReset && 'c_grid_container_reset');

    const onGridSizeChanged = useCallback(
      (params: GridSizeChangedEvent) => {
        // 현재 그리드의 너비
        const gridWidth = params.clientWidth;
        // 표시할 컬럼과 숨길 컬럼 저장
        const columnsToShow = [];
        const columnsToHide = [];
        // 모든 컬럼(보이는 또는 보이지 않는)을 반복하고 현재 그리드 너비에 따라 몇 개의 컬럼이 들어갈 수 있는지 계산
        let totalColsWidth = 0;
        const allColumns = params.api.getColumns();
        if (allColumns && allColumns.length > 0) {
          for (let i = 0; i < allColumns.length; i++) {
            const column = allColumns[i];
            totalColsWidth += column.getMinWidth();
            if (totalColsWidth > gridWidth) {
              columnsToHide.push(column.getColId());
            } else {
              columnsToShow.push(column.getColId());
            }
          }
        }
        // 현재 그리드 너비에 따라 컬럼 표시/숨김
        params.api.setColumnsVisible(columnsToShow, true);
        params.api.setColumnsVisible(columnsToHide, false);
        // 컬럼이 멈추고 나서 빈 공간을 채우기 위해 대기
        // 빈 공간이 없도록 보장
        window.setTimeout(() => {
          params.api.sizeColumnsToFit();
        }, 300);
      },
      [window]
    );

    return (
      <div className={containerClass}>
        <GridLoading isLoading={isLoading} />
        <AgGridReact
          className="ag-theme-quartz"
          ref={ref}
          overlayNoRowsTemplate={placeholder}
          columnDefs={columns}
          defaultColDef={{
            cellStyle: { textAlign: headerTextAlign },
            suppressMovable: !movable,
            sortable: sortable,
            resizable: resizable,
            ...defaultColDef,
          }}
          singleClickEdit={singleClickEdit}
          rowData={rowData}
          {...(autoSizeStrategy !== 'onGridSizeChanged'
            ? { autoSizeStrategy: autoSizeStrategy }
            : { onGridSizeChanged: onGridSizeChanged })}
          {...gridOptions}
        />
      </div>
    );
  }
);

export default Grid;
