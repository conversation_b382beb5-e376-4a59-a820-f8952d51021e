import { createPortal } from 'react-dom';
import ErrorMsg from '@components/common/Error/ErrorMsg';
import joinClassName from '@utils/joinClassName';
import React, { forwardRef, useEffect, useRef, useState } from 'react';

export type OptionType = { label: string; value: string | number };

interface SelectBoxProps {
  placeholder?: string; // 기본 문구
  options: OptionType[]; // 옵션
  selectedValue: OptionType; // 선택된 값
  setSelectedValue: React.Dispatch<React.SetStateAction<OptionType>>; // 외부로 value 전달
  defaultValue?: OptionType;
  error?: string;
  disabled?: boolean;
  className?: string;
  optionListClassName?: string;
}

/**
 *
 * TODO)
 * 1. disabled 옵션 추가
 * 2. 키보드 조작 시 추가
 */

/**기본 selectbox */
const SelectBox = forwardRef<HTMLDivElement, SelectBoxProps>(
  (
    {
      placeholder = '선택이 필요합니다', // placeholder
      options, // 옵션 리스트(*) {label, value}[]
      selectedValue, // 선택된 값
      setSelectedValue, // 선택된 값을 전달하는 state 함수 (*)
      defaultValue, // 기본 선택값
      error, // 에러시 출력메세지
      disabled = false,
      className,
      optionListClassName,
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const isOpenRef = useRef(null);
    const [selectedLabel, setSelectedLabel] = useState<string>(placeholder);
    const selectBoxRef = useRef(null);
    const [optionPosition, setOptionPosition] = useState({ top: 0, left: 0, width: 0 });

    /** 기본값이 있을 경우 */
    useEffect(() => {
      if (typeof defaultValue !== 'undefined') {
        setSelectedValue(defaultValue);
        setSelectedLabel(defaultValue.label);
      }
    }, []);

    useEffect(() => {
      defaultValue ? setSelectedLabel(defaultValue.label) : setSelectedLabel(placeholder);
    }, [defaultValue]);

    const handleClickSelectBox = () => {
      if (disabled) return;
      setIsOpen((prev) => !prev);
    };

    const handleGetValue = (idx: number) => {
      setSelectedValue(options[idx]);
      setSelectedLabel(options[idx].label);
      setIsOpen(false);
    };

    /** SelectBox 외부 클릭 시 SelectBox 닫기 */
    const handleClickOutside = (event) => {
      if (
        selectBoxRef.current &&
        !selectBoxRef.current.contains(event.target as Node) &&
        !document.getElementById('select-options').contains(event.target as Node) &&
        isOpenRef.current
      ) {
        setIsOpen(false);
      }
    };

    useEffect(() => {
      isOpenRef.current = isOpen; // portal로 열린 option dom 있는지 없는지 확인
    }, [isOpen]);

    useEffect(() => {
      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    const handleGetOptionPosition = (entry) => {
      if (!selectBoxRef.current) return;

      const rect = selectBoxRef.current.getBoundingClientRect(); // viewport 기준 위치
      const scrollX = window.scrollX; // 문서의 X 스크롤 값
      const scrollY = window.scrollY; // 문서의 Y 스크롤 값

      const width = entry.contentRect.width; // 초기에는 getBoundingClientRect로 width 확인
      const height = entry.contentRect.height;

      // 위치 및 크기 계산
      setOptionPosition({
        width: width,
        left: rect.left + scrollX,
        top: rect.top + scrollY + height + 4,
      });
    };

    useEffect(() => {
      // ResizeObserver - size 및 position 변화 감지
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          handleGetOptionPosition(entry); // observer에서 함수 호출
        }
      });

      if (selectBoxRef.current) {
        resizeObserver.observe(selectBoxRef.current);
      }

      // 클린업
      return () => {
        resizeObserver.disconnect();
      };
    }, [isOpen]);

    let optionStyle: React.CSSProperties = {
      top: `${optionPosition.top}px`,
      left: `${optionPosition.left}px`,
      width: `${optionPosition.width}px`,
    };

    useEffect(() => {
      const refPosition = selectBoxRef.current.getBoundingClientRect();

      setOptionPosition({
        top: Math.ceil(refPosition.top + refPosition.height),
        left: refPosition.left,
        width: refPosition.width,
      });
    }, [selectBoxRef.current]);

    return (
      <div className={joinClassName('c_select', isOpen && 'open', className)} ref={selectBoxRef}>
        {/* 라벨 영역 */}
        <div
          className="c_selected_label"
          onClick={handleClickSelectBox}
          data-selected-value={selectedValue?.value}
          aria-disabled={disabled}
        >
          <span>{selectedLabel}</span>
        </div>
        {/* 옵션 영역 */}
        {createPortal(
          <>
            {isOpen && (
              <div className={joinClassName('c_select_lists', optionListClassName)} style={optionStyle} ref={isOpenRef}>
                <ul className="c_select_lists_inner">
                  {options &&
                    options.map((option, idx) => (
                      <li
                        className={joinClassName(
                          'c_select_option_item',
                          selectedValue.value === option.value && 'selected'
                        )}
                        key={option.value}
                        onClick={() => handleGetValue(idx)}
                        data-value={option.value}
                      >
                        {option.label}
                      </li>
                    ))}
                </ul>
              </div>
            )}
          </>,
          document.getElementById('select-options')
        )}
        <ErrorMsg text={error} />
      </div>
    );
  }
);

export default SelectBox;
