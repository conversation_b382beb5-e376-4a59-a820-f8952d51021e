import { Menu } from 'types/adminMenuType';
/**
 * 코드 관련 API 호출을 위한 커스텀 훅
 */
export const useMenuAPI = () => {
  // "response 받은 메뉴 리스트"에서 "
  // "해당 ID에 허용된 모든 URL 경로"를 추출하는 함수
  // 하위 메뉴(childMenus)가 있는 경우 재귀적으로 처리
  const extractUrlsFromMenuList = (menuList: Menu[]) => {
    // 메뉴 리스트에서 URL 추출
    const paths = menuList.reduce<string[]>((acc, menu) => {
      // 현재 메뉴의 URL을 추가
      if (menu.url) {
        acc.push(menu.url);
      }

      // 하위 메뉴가 있는 경우 재귀적으로 URL 추출
      if (menu.childs?.length) {
        const childUrls = extractUrlsFromMenuList(menu.childs);
        acc.push(...childUrls);
      }

      return acc;
    }, []);

    return paths;
  };

  return {
    extractUrlsFromMenuList,
  };
};
