import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import Pagination from '@components/common/Pagination';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { AgGridReact } from '@ag-grid-community/react';
import { ColDef, ColGroupDef, RowSelectedEvent } from '@ag-grid-community/core';
import { useConfirmStore } from '@store/useConfirmStore';
import SelectBox from '@components/common/SelectBox/SelectBox';
import Grid from '@components/common/Grid/Grid';
import { ListFormValues, UserListParams } from '@page/Admin/Account/type';
import { useAdminAccountListStore } from './store/useAdminAccountListStore';
import { useAlertStore } from '@store/useAlertStore';
import { getAccountList, deleteAccountList } from '@api/admin/accountListAPI';
import { useNavigate } from 'react-router-dom';
import { calcNoSort } from '@utils/calcNoSort';
import { pageSizeOptions } from '@store/constant';

// static
const activeType = [
  { label: '전체', value: '' },
  { label: '잠금', value: 'Y' },
  { label: '해제', value: 'N' },
];

const AdminAccountListPage = () => {
  const navigate = useNavigate();
  const {
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    adminListData,
    paginationData,
    setAdminListData,
    setPaginationData,
  } = useAdminAccountListStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();
  // 검색 폼 관리
  const searchMethods = useForm<ListFormValues>({
    defaultValues: {
      adminId: '', // 관리자 아이디 검색어 초기값
      name: '', // 관리자 이름 검색어 초기값
      lockYn: { label: '전체', value: '' }, // 활성 상태 코드 초기값
    },
  });
  const [selectedPageSize, setSelectedPageSize] = useState(pageSizeOptions[1]);
  const gridRef = useRef<AgGridReact>(null);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const LoginFailureHeader = () => {
    return (
      <div className="ag-header-custom-login_failure">
        <p>로그인 실패횟수</p>
        <span>(5회 초과 시 잠금)</span>
      </div>
    );
  };

  const handleCellClicked = (event: any) => {
    navigate(`/admin/account/detail/${event.data.id}`);
  };

  const [columnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    { headerName: 'No.', field: 'sort', width: 100 },
    {
      headerName: '관리자ID',
      field: 'adminId',
      flex: 1,
    },
    {
      headerName: '관리자 이름',
      field: 'name',
      flex: 1,
    },
    {
      headerName: '로그인 실패횟수',
      headerComponent: LoginFailureHeader,
      field: 'loginFailureCnt',
      flex: 1,
    },
    {
      headerName: '계정 삭제 여부',
      field: 'delYn',
      flex: 1,
      cellRenderer: (params: any) => {
        return <span>{params.value === 'Y' ? '삭제됨' : ''}</span>;
      },
    },
    { headerName: '등록자', field: 'dateInfo.createUser', flex: 1 },
    { headerName: '등록시간', field: 'dateInfo.createDate', flex: 1 },
    { headerName: '수정자', field: 'dateInfo.updateUser', flex: 1 },
    { headerName: '수정시간', field: 'dateInfo.updateDate', flex: 1 },
  ]);

  const parsedGridData = (data: any, pageInfo: any) => {
    return data.map((item: any, index: number) => ({
      ...item,
      loginFailureCnt: item.loginFailureCnt || 0,
      sort: calcNoSort({ totalCount: pageInfo.totalCount, currentPage, pageSize, index }),
    }));
  };

  const getAccountListData = async () => {
    const postData: UserListParams = {
      page: currentPage,
      size: pageSize,
      ...searchMethods?.getValues(),
      lockYn: searchMethods.getValues('lockYn')?.value,
    };

    const { data, pageInfo } = await getAccountList({ data: postData });

    const parsedData = parsedGridData(data, pageInfo);
    setAdminListData(parsedData);
    setPaginationData(pageInfo);
  };

  const onRowSelected = useCallback((event: RowSelectedEvent) => {
    const selectedNodes = gridRef.current?.api.getSelectedNodes();
    const selectedData = selectedNodes?.map((node) => node.data) || [];
    setSelectedRows(selectedData);
  }, []);

  const handleClickDelete = () => {
    const selectedRowNames = selectedRows.map((row) => row.adminId).join(', ');
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: (
        <>
          <hr className="my-2" />
          <span className="text-red-500 font-bold">{selectedRowNames}</span>
          <hr className="my-2" />
          계정을 삭제 하시겠습니까?
        </>
      ),
      onCancel: initConfirmState,
      onConfirm: async () => {
        const message = await deleteAccountList({
          ids: selectedRows.map((row) => row.id),
        });
        activeAlert(message);
        initConfirmState();
      },
    });
  };

  // 페이지 조회 시 데이터 조회
  useEffect(() => {
    getAccountListData();
  }, [currentPage, pageSize]);

  // pageSize 변경 시 페이지 초기화
  useEffect(() => {
    setCurrentPage(0);
    setPageSize(selectedPageSize.value);
  }, [selectedPageSize]);

  return (
    <div className="manage_admin_account">
      {/* 검색 폼 */}
      <Form onSubmit={getAccountListData} methods={searchMethods}>
        <ControlBox>
          <FormSelectBox name="lockYn" label="계정 잠금 여부" options={activeType} />
          <FormInput name="adminId" label="관리자 아이디" placeholder="관리자 아이디를 입력하세요" />
          <FormInput name="name" label="관리자 이름" placeholder="관리자 이름을 입력하세요" />
          <Button type="submit" text="조회" clickLog={{ buttonSection: '검색창' }} />
        </ControlBox>
      </Form>
      <TableContainerHeader
        leftChildren={
          <>
            <p>총 {paginationData?.totalCount}건</p>
            <SelectBox
              options={pageSizeOptions}
              defaultValue={selectedPageSize}
              selectedValue={selectedPageSize}
              setSelectedValue={setSelectedPageSize}
            />
          </>
        }
        rightChildren={
          <>
            <Button
              text="삭제"
              color="red"
              disabled={selectedRows.length === 0}
              onClick={handleClickDelete}
              clickLog={{ buttonSection: '목록' }}
            />
            <Button text="추가" onClick={() => navigate('/admin/account/add')} clickLog={{ buttonSection: '목록' }} />
          </>
        }
      />
      <Grid
        placeholder="데이터가 없습니다. 코드를 추가해주세요."
        ref={gridRef}
        columns={columnDefs}
        defaultColDef={{
          width: 170,
          onCellClicked: handleCellClicked,
        }}
        rowData={adminListData}
        gridOptions={{
          onRowSelected: onRowSelected,
          rowSelection: {
            checkboxes: true,
            headerCheckbox: true,
            mode: 'multiRow',
          },
        }}
      />
      {paginationData && (
        <Pagination
          currentPage={currentPage + 1}
          itemCountPerPage={pageSize}
          onPageChange={(e) => setCurrentPage(e - 1)}
          totalCount={paginationData.totalCount}
        />
      )}
    </div>
  );
};

export default AdminAccountListPage;
