import { useForm } from 'react-hook-form';

import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import Button from '@components/common/Button/Button';

import { AdminBoardAddFormValue, AdminBoardAddOptions } from '@page/Admin/Board/type';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { deleteBoard, getBoardDetail, postBoard, putBoard, getBoardCategoryList } from '@api/admin/boardApi';
import { getCodeList } from '@api/admin/systemCodeAPI';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';

const BOARD_TYPE_GROUP = 'TEMPLET_TYPE';

const AdminBoardUpsert = () => {
  const { id } = useParams();
  const type = id ? "edit" : "add";

  // 셀렉트 옵션
  const [boardTypes, setBoardTypes] = useState<AdminBoardAddOptions['boardType']>([
    { label: '선택이 필요합니다', value: '' },
  ]);

  const fileUseYn: AdminBoardAddOptions['useYn'] = [
    { label: '사용', value: 'Y' },
    { label: '미사용', value: 'N' },
  ];

  const commentUseYn: AdminBoardAddOptions['useYn'] = [
    { label: '사용', value: 'Y' },
    { label: '미사용', value: 'N' },
  ];

  const [categoryA, setCategoryA] = useState<AdminBoardAddOptions['category']>([
    { label: '선택이 필요합니다', value: '' },
  ]);

  const [categoryB, setCategoryB] = useState<AdminBoardAddOptions['category']>([
    { label: '선택이 필요합니다', value: '' },
  ]);

  const boardUseYn: AdminBoardAddOptions['useYn'] = [
    { label: '사용', value: 'Y' },
    { label: '미사용', value: 'N' },
  ];

  const initValues: AdminBoardAddFormValue = {
    boardType: { label: '선택이 필요합니다', value: '' },
    boardName: '',
    fileUseYn: { label: '선택이 필요합니다', value: 'none' },
    commentUseYn: { label: '선택이 필요합니다', value: 'none' },
    categoryA: { label: '선택이 필요합니다', value: '' },
    categoryB: { label: '선택이 필요합니다', value: '' },
    extraInfo: '',
    boardUseYn: { label: '선택이 필요합니다', value: 'none' },
  };

  // -----------------------------------------------------------------------------------------
  const [defaultValues, setDefaultValues] = useState<AdminBoardAddFormValue>(initValues);
  const methods = useForm<AdminBoardAddFormValue>({ defaultValues });
  const navigate = useNavigate();
  const { setAlertState, initAlertState } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();

  const { reset, getValues } = methods;

  const getBoardTypeList = async () => {
    const boardTypeRes = await getCodeList({ code: BOARD_TYPE_GROUP });

    setBoardTypes(
      boardTypeRes.map((boardType) => {
        return { label: boardType.name, value: boardType.code };
      })
    );
  };

  const getCategoryList = async () => {
    const categoryRes = await getBoardCategoryList({cateSeq : null}); 
    const options = categoryRes.map((category) => ({
      label: category.name,
      value: category.seq,
    }));
    options.unshift({ label: '선택이 필요합니다', value: '' });

    setCategoryA(options);
    setCategoryB(options);
  };

  useEffect(() => {
    console.log("init", type);

    if (type === 'add') {
      getBoardTypeList();
      getCategoryList();
      
    } else {
      const getBoardDetailData = async () => {
        const [response, cateOptionRes] = await Promise.all([
          getBoardDetail({ id }), 
          getBoardCategoryList({cateSeq : null})
        ]);

        const options = cateOptionRes.map((category) => ({
          label: category.name,
          value: category.seq,
        }));
        options.unshift({ label: '선택이 필요합니다', value: '' });

        setCategoryA(options);
        setCategoryB(options);

        const detailData: AdminBoardAddFormValue = {
          boardType: { label: response.templetTypeNm, value: response.templetTypeCd },
          boardName: response.templetNm,
          fileUseYn: { label: response.fileUseYn === 'Y' ? '사용' : '미사용', value: response.fileUseYn },
          commentUseYn: { label: response.replyUseYn === 'Y' ? '사용' : '미사용', value: response.replyUseYn },
          categoryA: {
            label: utils.getLabel(response?.cateGrpCd1, options) || (type === 'edit' ? '선택이 필요합니다' : ''),
            value: response.cateGrpCd1 || '',
          },
          categoryB: {
            label: utils.getLabel(response?.cateGrpCd2, options) || (type === 'edit' ? '선택이 필요합니다' : ''),
            value: response.cateGrpCd2 || '',
          },
          extraInfo: response.additionInfoDesc,
          boardUseYn: { label: response.useYn === 'Y' ? '사용' : '미사용', value: response.useYn },
        };

        reset(detailData);
        setDefaultValues(detailData);
      };

      getBoardDetailData();
    }

  }, [type]);

  const isFieldValid = (data: AdminBoardAddFormValue) => {
    const { boardType, boardName, fileUseYn, commentUseYn, boardUseYn } = data;

    const isValid = (value) => typeof value === 'string' && value.trim() !== '' && value !== 'none';
    const alertMsg = (field: string) => {
      return setAlertState({
        isOpen: true,
        content: field,
        onConfirm: initAlertState,
      });
    };

    const validations = [
      ...(type === 'add' ? [{ value: boardType.value, message: '게시판 유형을 선택해 주세요' }] : []),
      { value: boardName, message: '게시판 명을 입력해 주세요' },
      { value: fileUseYn.value, message: '첨부파일 사용 유무를 선택해 주세요' },
      { value: commentUseYn.value, message: '댓글 사용 유무를 선택해 주세요' },
      { value: boardUseYn.value, message: '게시판 사용 유무를 선택해 주세요' },
    ];

    for (const { value, message } of validations) {
      if (!isValid(value)) {
        alertMsg(message);
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (data: AdminBoardAddFormValue) => {
    const { boardType, boardName, fileUseYn, commentUseYn, categoryA, categoryB, extraInfo, boardUseYn } = data;

    if (isFieldValid(data)) {
      const postData = {
        templetTypeCd: boardType.value,
        templetNm: boardName,
        cateGrpCd1: categoryA.value,
        cateGrpCd2: categoryB.value,
        additionInfoDesc: extraInfo,
        fileUseYn: fileUseYn.value,
        replyUseYn: commentUseYn.value,
        useYn: boardUseYn.value,
      };

      if (type === 'add') {
        const response = await postBoard(postData);

        if (response === 'CREATE') {
          setAlertState({
            isOpen: true,
            content: '게시판이 등록되었습니다',
            onConfirm: () => {
              navigate(`/admin/board`);
              initAlertState();
            },
          });
        }
      } else if (type === 'edit') {
        const response = await putBoard({ id, ...postData });

        if (response === 'UPDATE') {
          setAlertState({
            isOpen: true,
            content: '데이터를 성공적으로 수정하였습니다',
            onConfirm: () => {
              navigate(`/admin/board/detail/${id}`);
              initAlertState();
            },
          });
        }
      }
    }
  };

  // 초기화
  const handleReset = () => {
     reset(defaultValues);
  };

  return (
    <div className="admin_board">
      <Form onSubmit={handleSubmit} methods={methods}>
        <TableContainer className="admin_board_add_form">
          <TableBody>
            <TableBodyRow
              rowData={{
                title: '게시판 유형',
                required: true,
                contents: (
                  <>
                    {type !== 'add' ? (
                      <p>{defaultValues.boardType.label}</p>
                    ) : (
                      <FormSelectBox name="boardType" options={boardTypes} />
                    )}
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '게시판 명',
                required: true,
                contents: (
                  <>
                    <FormInput name="boardName" placeholder="게시판 이름을 입력해 주세요" />  
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '첨부파일 사용 유무',
                required: true,
                contents: (
                  <>
                    <FormSelectBox name="fileUseYn" options={fileUseYn} />
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '댓글 사용 유무',
                required: true,
                contents: (
                  <>
                    <FormSelectBox name="commentUseYn" options={commentUseYn} />
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '카테고리 선택1',
                contents: (
                  <>
                    <div>
                        <FormSelectBox name="categoryA" options={categoryA} />
                    </div>
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '카테고리 선택2',
                contents: (
                  <>
                    <div>
                        <FormSelectBox name="categoryB" options={categoryB} />
                    </div>
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '부가 정보',
                contents: (
                  <>
                    <FormInput name="extraInfo" placeholder="부가 정보를 입력해 주세요" />
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '게시판 사용 유무',
                required: true,
                contents: (
                  <>
                    <FormSelectBox name="boardUseYn" options={boardUseYn} />
                  </>
                ),
              }}
            />
          </TableBody>
        </TableContainer>
        <div className="admin_board_add_btn">
          <Button text="취소" onClick={() => navigate(type === 'edit' ? `/admin/board/detail/${id}` : `/admin/board`)} />
          <div className="btn_right">
            <>
              <Button text="초기화" color="grayscale" onClick={handleReset} />
              <Button text={type === 'add' ? '등록' : '저장'} type="submit" />
            </>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default AdminBoardUpsert;
