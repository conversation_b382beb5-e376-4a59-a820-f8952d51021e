import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import Button from '@components/common/Button/Button';
import TableContainer from '@components/common/Table/TableContainer';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import { Page } from '../../type';
import { getPageDetail, getTemplateOptions, deletePage } from '@api/user/pageApi';
import { useYnOptions, pageTypeOptions, requiredYnOptions } from '@constants/options';
import { useNavigate } from 'react-router-dom';
import TextEditor from '@components/common/TextEditor/TextEditor';
import TableCell from '@components/common/Table/TableCell';

type PageInfoProps = {
  pageId?: string;
};

const PageDetail = ({ pageId }: PageInfoProps) => {
  const [pageInfo, setPageInfo] = useState<Page>(null);
  const [templateOptions, setTemplateOptions] = useState([]);
  const navigate = useNavigate();
  const formMethods = useForm({
    defaultValues: {
      pageName: '',
      pageFileName: '',
      filePath: '',
      pageTypeCode: {
        label: pageTypeOptions[0].label,
        value: pageTypeOptions[0].value,
      },
      roleUseYn: { label: useYnOptions[0].label, value: useYnOptions[0].value },
      pageDesc: '',
      pageContent: '',
      pageLink: '',
      boardTempletId: { label: '선택하세요', value: '' },
    },
  });

  const loadData = async () => {
    const [pageDetail, templateData] = await Promise.all([getPageDetail(pageId), getTemplateOptions(pageId)]);

    const options = templateData.map((item) => ({
      value: item.id,
      label: item.templetNm,
    }));

    setTemplateOptions(options);
    setPageInfo(pageDetail);
  };

  useEffect(() => {
    if (pageId) {
      loadData();
    }
  }, [pageId]);

  const onNavigateList = () => {
    navigate('/admin/page');
  };

  const onClickBtnDelete = () => {
    utils.showConfirm('delete', async () => {
      const res = await deletePage(pageId);
      if (res) {
        utils.showAlert(res, onNavigateList);
      }
    });
  };

  return (
    <>
      <TableContainer>
        <TableBody>
          <TableBodyRow
            rowData={[
              {
                title: '유형',
                contents: utils.getLabel(pageInfo?.pageTypeCode, pageTypeOptions),
              },
              {
                title: '페이지명',
                contents: pageInfo?.pageName,
              },
            ]}
          />
          <TableBodyRow
            rowData={[
              {
                title: '페이지 파일명',
                contents: pageInfo?.pageFileName,
              },
              {
                title: '페이지 파일 경로',
                contents: pageInfo?.filePath,
              },
            ]}
          />
          {pageInfo?.pageTypeCode === 'B' && (
            <TableBodyRow
              rowData={[
                {
                  title: '로그인 필요 여부',
                  contents: utils.getLabel(pageInfo?.roleUseYn, requiredYnOptions),
                },
                {
                  title: '게시판 선택',
                  isFullWidth: true,
                  contents: utils.getLabel(pageInfo?.boardTempletId, templateOptions),
                },
              ]}
            />
          )}
          {pageInfo?.pageTypeCode === 'N' && (
            <TableBodyRow
              rowData={{
                title: 'URL',
                isFullWidth: true,
                contents: pageInfo?.pageLink,
              }}
            />
          )}
          {formMethods.watch('pageTypeCode').value === 'C' && (
            <TableBodyRow
              rowData={{
                title: '콘텐츠',
                contents: pageInfo?.pageContent,
              }}
            />
          )}
          <TableBodyRow
            rowData={{
              title: '설명',
              isFullWidth: true,
              contents: pageInfo?.pageDesc,
            }}
          />
          <TableBodyRow
            rowData={[
              {
                title: '수정자',
                contents: pageInfo?.dateInfo?.updateUser,
              },
              {
                title: '수정 일시',
                contents: pageInfo?.dateInfo?.updateDate,
              },
            ]}
          />
          <TableBodyRow
            rowData={[
              {
                title: '등록자',
                contents: pageInfo?.dateInfo?.createUser,
              },
              {
                title: '등록 일시',
                contents: pageInfo?.dateInfo?.createDate,
              },
            ]}
          />
          {pageInfo?.pageContent && pageInfo?.pageTypeCode === 'C' && (
            <>
              <tr>
                <TableCell colSpan={4} tag="th" >
                  페이지 콘텐츠
                </TableCell>
              </tr>
              <tr>
                <TableCell colSpan={4}>
                  <TextEditor className="admin_page_detail_contents" content={pageInfo?.pageContent} readOnly />
                </TableCell>
              </tr>
            </>
          )}
        </TableBody>
      </TableContainer>
      <div className="button_wrapper">
        <div className="left">
          <Button text="목록" onClick={onNavigateList} />
        </div>
        <div className="right">
          <Button text="삭제" color="red" onClick={onClickBtnDelete} />
          <Button
            text="수정"
            onClick={() => {
              navigate(`/admin/page/edit/${pageId}`);
            }}
          />
        </div>
      </div>
    </>
  );
};

export default PageDetail;
