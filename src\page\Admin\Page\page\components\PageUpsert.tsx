import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import Button from '@components/common/Button/Button';
import TableContainer from '@components/common/Table/TableContainer';
import TableBody from '@components/common/Table/TableBody';
import FormInput from '@components/common/Form/FormInput';
import Form from '@components/common/Form/Form';
import FormTextarea from '@components/common/Form/FormTextarea';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import { Page, PageDTO } from '../../type';
import type { PageMode } from '@type/pageMode';
import { getPageDetail, getTemplateOptions, updatePage, deletePage, createPage } from '@api/user/pageApi';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import { pageTypeOptions, requiredYnOptions } from '@constants/options';
import { useNavigate } from 'react-router-dom';
import TextEditor from '@components/common/TextEditor/TextEditor';

type PageInfoProps = {
  pageId?: string;
};

const PageUpsert = ({ pageId }: PageInfoProps) => {
  const mode = pageId ? 'edit' : 'add';
  const [pageInfo, setPageInfo] = useState<Page>(null);
  const [templateOptions, setTemplateOptions] = useState([]);
  const navigate = useNavigate();
  const [contents, setContents] = useState<string>('');
  const formMethods = useForm({
    defaultValues: {
      pageName: '',
      pageFileName: '',
      filePath: '',
      pageTypeCode: {
        label: pageTypeOptions[0].label,
        value: pageTypeOptions[0].value,
      },
      roleUseYn: { label: requiredYnOptions[0].label, value: requiredYnOptions[0].value },
      pageDesc: '',
      pageContent: '',
      pageLink: '',
      boardTempletId: { label: '선택하세요', value: 0 },
    },
  });

  const loadData = async () => {
    if (mode === 'add') {
      const templateData = await getTemplateOptions(pageId);
      const options = templateData.map((item) => ({
        value: item.id,
        label: item.templetNm,
      }));
      setTemplateOptions(options);
    } else {
      const [pageDetail, templateData] = await Promise.all([getPageDetail(pageId), getTemplateOptions(pageId)]);
      const options = templateData.map((item) => ({
        value: item.id,
        label: item.templetNm,
      }));
      setTemplateOptions(options);
      setPageInfo(pageDetail);

      if (pageDetail.pageContent) {
        setContents(pageDetail.pageContent);
      }

      formMethods.reset({
        pageName: pageDetail.pageName,
        pageFileName: pageDetail.pageFileName,
        filePath: pageDetail.filePath,
        pageTypeCode: {
          label: utils.getLabel(pageDetail.pageTypeCode, pageTypeOptions),
          value: pageDetail.pageTypeCode,
        },
        roleUseYn: {
          label: utils.getLabel(pageDetail.roleUseYn, requiredYnOptions),
          value: pageDetail.roleUseYn,
        },
        pageDesc: pageDetail.pageDesc,
        pageContent: pageDetail.pageContent,
        pageLink: pageDetail.pageLink,
        boardTempletId: {
          label: utils.getLabel(pageDetail.boardTempletId, options),
          value: pageDetail.boardTempletId,
        },
      });
    }
  };

  useEffect(() => {
    loadData();
  }, [mode]);

  const getInvalidFields = (formData) => {
    const isBlank = (val) => _.isEmpty(_.trim(val));
    const { pageName, pageTypeCode, boardTempletId, pageLink, pageContent, filePath, pageFileName } = formData;

    const validList = [
      { name: 'pageName', value: pageName },
      { name: 'filePath', value: filePath },
      { name: 'pageFileName', value: pageFileName },
    ];

    if (pageTypeCode === 'C') {
      validList.push({ name: 'pageContent', value: pageContent });
    } else if (pageTypeCode === 'N') {
      validList.push({ name: 'pageLink', value: pageLink });
    } else if (pageTypeCode === 'B') {
      validList.push({ name: 'boardTempletId', value: boardTempletId });
    }

    // 유효하지 않은 필드들만 배열로 반환
    return validList.filter((field) => isBlank(field.value)).map((field) => field.name);
  };

  const getReqPayload = (formData: PageDTO) => {
    const flattenedData = utils.flattenValues(formData);

    if (contents) {
      flattenedData.pageContent = contents;
    }

    if (flattenedData.pageTypeCode === 'C') {
      delete flattenedData.pageLink;
      delete flattenedData.boardTempletId;
    } else if (flattenedData.pageTypeCode === 'N') {
      delete flattenedData.pageContent;
      delete flattenedData.boardTempletId;
    } else if (flattenedData.pageTypeCode === 'B') {
      delete flattenedData.pageContent;
      delete flattenedData.pageLink;
    } else {
      delete flattenedData.pageLink;
      delete flattenedData.pageContent;
      delete flattenedData.boardTempletId;
    }

    return flattenedData;
  };

  const getAlertMsg = (inValidFieldList) => {
    const fieldMessages = {
      pageName: '페이지명을 입력해주세요.',
      filePath: '페이지 파일 경로를 입력해주세요.',
      pageFileName: '페이지 파일명을 입력해주세요.',
      boardTempletId: '게시판을 선택해주세요.',
      pageLink: '페이지 링크를 입력해주세요.',
      pageContent: '페이지 콘텐츠를 입력해주세요.',
    };

    const messages = inValidFieldList.map((field) => fieldMessages[field]);
    return messages[0];
  };

  const formSubmit = async (formData: PageDTO) => {
    const reqPayload = getReqPayload(formData);

    const inValidFieldList = getInvalidFields(reqPayload);

    if (!_.isEmpty(inValidFieldList)) {
      const message = getAlertMsg(inValidFieldList);
      utils.showAlert(message);
      return;
    }

    utils.showConfirm('save', async () => {
      const res = mode === 'edit' ? await updatePage(pageId, reqPayload) : await createPage(reqPayload);
      if (mode === 'add') {
        utils.showAlert(res, onNavigateList);
      } else {
        utils.showAlert(res, onNavigateDetail);
      }
    });
  };

  const onNavigateList = () => {
    navigate('/admin/page');
  };

  const onNavigateDetail = () => {
    navigate(`/admin/page/detail/${pageId}`);
  };

  const onClickBtnCancel = () => {
    if (mode === 'add') {
      utils.showConfirm('cancel', onNavigateList);
    } else {
      utils.showConfirm('cancel', onNavigateDetail);
    }
  };

  return (
    <>
      <Form methods={formMethods} onSubmit={formSubmit}>
        <TableContainer>
          <TableBody>
            <TableBodyRow
              rowData={[
                {
                  title: '유형',
                  required: true,
                  contents: <FormSelectBox name="pageTypeCode" options={pageTypeOptions} />,
                },
                {
                  title: '페이지명',
                  required: true,
                  contents: <FormInput name="pageName" placeholder="페이지명 입력" />,
                },
              ]}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '페이지 파일명',
                  required: true,
                  contents: <FormInput name="pageFileName" placeholder="페이지 파일명 입력" />,
                },
                {
                  title: '페이지 파일 경로',
                  required: true,
                  contents: <FormInput name="filePath" placeholder="페이지 파일 경로 입력" />,
                },
              ]}
            />
            <TableBodyRow
              rowData={{
                title: '로그인 필요 여부',
                required: true,
                isFullWidth: true,
                contents: <FormSelectBox name="roleUseYn" options={requiredYnOptions} />,
              }}
            />
            {formMethods.watch('pageTypeCode').value === 'B' && (
              <TableBodyRow
                rowData={[
                  {
                    title: '게시판 선택',
                    isFullWidth: true,
                    required: true,
                    contents: <FormSelectBox name="boardTempletId" options={templateOptions} />,
                  },
                ]}
              />
            )}
            {formMethods.watch('pageTypeCode').value === 'N' && (
              <TableBodyRow
                rowData={{
                  title: 'URL',
                  isFullWidth: true,
                  required: true,
                  contents: <FormTextarea name="pageLink" placeholder="링크 입력" />,
                }}
              />
            )}
            <TableBodyRow
              rowData={{
                title: '설명',
                isFullWidth: true,
                contents: <FormTextarea name="pageDesc" placeholder="페이지 설명 입력" />,
              }}
            />
            {mode === 'edit' && (
              <>
                <TableBodyRow
                  rowData={[
                    {
                      title: '수정자',
                      contents: pageInfo?.dateInfo?.updateUser,
                    },
                    {
                      title: '수정 일시',
                      contents: pageInfo?.dateInfo?.updateDate,
                    },
                  ]}
                />
                <TableBodyRow
                  rowData={[
                    {
                      title: '등록자',
                      contents: pageInfo?.dateInfo?.createUser,
                    },
                    {
                      title: '등록 일시',
                      contents: pageInfo?.dateInfo?.createDate,
                    },
                  ]}
                />
              </>
            )}
            {formMethods.watch('pageTypeCode').value === 'C' && (
              <>
                <tr>
                  <th colSpan={4}>페이지 콘텐츠</th>
                </tr>
                <tr>
                  <td colSpan={4}>
                    <TextEditor
                      className={utils.joinClassName('admin_post_detail_contents')}
                      content={contents}
                      onChange={setContents}
                    />
                  </td>
                </tr>
              </>
            )}
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button text="취소" onClick={onClickBtnCancel} color={mode === 'add' ? 'red' : 'grayscale'} />
          <Button text="저장" type="submit" />
        </div>
      </Form>
    </>
  );
};

export default PageUpsert;
