import { AgGridReact } from '@ag-grid-community/react';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import { useEffect, useRef, useState } from 'react';
import { ColDef } from '@ag-grid-community/core';
import SubTitle from '@components/common/Title/SubTitle';
import DataGrid from '@components/common/Grid/DataGrid';
import { Page } from '../../type';
import { getSubPagesList, deleteSubPages, getTemplateOptions } from '@api/admin/pageApi';
import { pageTypeOptions } from '@constants/options';
import { useNavigate } from 'react-router-dom';
import SubPageUpsertModal from './SubPageUpsertModal';
import useEventBus from '@hooks/useEventBus';

interface Props {
  pageId?: Page['id'];
}

const SubPageList = ({ pageId }: Props) => {
  const [rowData, setRowData] = useState<Page[]>([]);
  const [modalMode, setModalMode] = useState<string>('');
  const [childId, setChildId] = useState<string>('');
  const navigate = useNavigate();
  const gridRef = useRef<AgGridReact>(null);
  const [selectedRow, setSelectedRow] = useState([]);
  const { eventBus } = useEventBus();

  eventBus.on('upsert', (payload) => {
    if (payload.message === 'save') fetchData();
  });

  const mapTemplateName = (data, templateData) => {
    // templateData를 id 기준으로 빠르게 조회할 수 있는 객체로 변환
    const templateMap = _.keyBy(templateData, 'id');

    // data에 templetNm 속성을 추가
    return data.map((item) => {
      if (item.boardTempletId != null) {
        const matchedTemplate = templateMap[item.boardTempletId];
        return {
          ...item,
          templetNm: matchedTemplate ? matchedTemplate.templetNm : null,
        };
      }
      return { ...item, templetNm: null };
    });
  };

  const fetchData = async () => {
    const [data, templateData] = await Promise.all([getSubPagesList(pageId), getTemplateOptions(pageId)]);
    const newData = mapTemplateName(data, templateData);
    setRowData(newData);
  };

  useEffect(() => {
    if (!pageId) return;
    fetchData();
  }, [pageId]);

  const [columnDefs] = useState<ColDef[]>([
    {
      field: 'pageTypeCode',
      headerName: '유형',
      cellClass: 'text-center',
      valueFormatter: ({ value }) => {
        return utils.getLabel(value, pageTypeOptions);
      },
    },
    {
      field: 'pageName',
      headerName: '페이지명',
      cellClass: '',
    },
    {
      field: 'filePath',
      headerName: '페이지 파일 경로',
    },
    {
      field: 'roleUseYn',
      headerName: '로그인 필요 여부',
      cellClass: 'text-center',
    },
    {
      field: 'templetNm',
      headerName: '게시판',
    },
    {
      field: 'pageLink',
      headerName: '링크',
    },
    {
      field: 'pageContent',
      headerName: '콘텐츠',
    },
    {
      field: 'createUser',
      headerName: '등록자',
      valueGetter: ({ data }) => data?.dateInfo?.createUser ?? '',
    },
    {
      field: 'createDate',
      headerName: '등록 일시',
      valueGetter: ({ data }) => data?.dateInfo?.createDate ?? '',
    },
    {
      field: 'updateUser',
      headerName: '수정자',
      valueGetter: ({ data }) => data?.dateInfo?.updateUser ?? '',
    },
    {
      field: 'updateDate',
      headerName: '수정 일시',
      valueGetter: ({ data }) => data?.dateInfo?.updateDate ?? '',
    },
  ]);

  // 행 선택
  const onRowSelected = (event: any) => {
    setSelectedRow((prev) => {
      if (prev.includes(event.data.id)) {
        return prev.filter((id) => id !== event.data.id);
      }
      return [...prev, event.data.id];
    });
  };

  const onClickBtnDeleteSubPage = () => {
    utils.showConfirm('delete', async () => {
      const data = { parentId: pageId, ids: [...selectedRow] };
      const res = await deleteSubPages(data);
      if (res) {
        utils.showAlert(res, fetchData);
      }
    });
  };

  return (
    <>
      {/* 테이블 헤더 영역 */}
      <SubTitle>페이지 하위 목록</SubTitle>
      <DataGrid
        ref={gridRef}
        rowEditMode={false}
        addButtonCallback={() => {
          setModalMode('add');
        }}
        deleteButtonCallback={() => onClickBtnDeleteSubPage()}
        columnDefs={columnDefs}
        rowData={rowData}
        gridOptions={{
          onRowSelected: onRowSelected,
          rowSelection: {
            checkboxes: true,
            headerCheckbox: true,
            mode: 'multiRow',
          },
        }}
        getRowClass={() => 'cursor-pointer'}
        defaultColDef={{
          onCellClicked: (event) => {
            setModalMode('edit');
            setChildId(event.data.id);
          },
        }}
      />
      <SubPageUpsertModal modalMode={modalMode} onClose={() => setModalMode('')} parentId={pageId} childId={childId} />
    </>
  );
};

export default SubPageList;
