import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { MenuData } from '@page/Admin/Role/type';
import CheckboxTree from 'react-checkbox-tree';
import 'react-checkbox-tree/src/scss/react-checkbox-tree.scss';

interface MenuAuthListProps {
  menuListData: MenuData[];
  checkedList: number[];
  setCheckedList: Dispatch<SetStateAction<number[]>>;
}

const MenuAuthList = ({ menuListData, checkedList, setCheckedList }: MenuAuthListProps) => {
  const [menuData, setMenuData] = useState([]);
  const [pMenuId, setPMenuId] = useState([]);

  // 1depth 메뉴 리스트
  useEffect(() => {
    if (menuListData) {
      const pMenuIds: string[] = [];

      const menuList = menuListData.map((menu) => {
        pMenuIds.push(String(menu.id));

        return {
          className: 'depth1_menu',
          label: menu.name,
          value: menu.id,
          children: menu.childs.map((child) => {
            pMenuIds.push(String(child.id));
            return {
              className: 'depth2_menu',
              label: child.name,
              value: child.id,
              children: child.childs.map((bChild) => {
                return { className: 'depth3_menu', label: bChild.name, value: bChild.id };
              }),
            };
          }),
        };
      });

      setMenuData(menuList);
      setPMenuId(pMenuIds);
    }
  }, [menuListData]);

  return (
    <div className="menu_auth_contents">
      <CheckboxTree
        nodes={menuData}
        checked={checkedList.map(String)}
        onCheck={(checked) => setCheckedList(checked.map(Number))}
        expanded={pMenuId}
        onExpand={(expanded) => setPMenuId(expanded)}
        checkModel="all"
      />
    </div>
  );
};

export default MenuAuthList;
