import Grid from '@components/common/Grid/Grid';
import { ParsedServiceData } from '@page/Admin/Role/type';
import { checkedRows } from '@page/Admin/Role/utils/checkedRows';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';

interface ServiceAuthListProps {
  serviceListData: ParsedServiceData[];
  serviceGroupIds: number[];
  setServiceGroupIds: Dispatch<SetStateAction<number[]>>;
  serviceIds: number[];
  setServiceIds: Dispatch<SetStateAction<number[]>>;
}

const ServiceAuthList = ({
  serviceListData,
  serviceGroupIds,
  setServiceGroupIds,
  serviceIds,
  setServiceIds,
}: ServiceAuthListProps) => {
  const serviceGroupApi = useRef(null);
  const serviceApi = useRef(null);

  const [serviceGroupList, setServiceGroupList] = useState([]);
  const [serviceList, setServiceList] = useState([]);
  const [selectedServiceGroupId, setSelectedServiceGroupId] = useState(0);
  const [serviceHeaderName, setServiceHeaderName] = useState('');

  // 서비스 그룹 데이터
  useEffect(() => {
    if (serviceListData) {
      setServiceGroupList(
        serviceListData.map((serviceGroup, idx) => {
          // 초기값 선택
          if (idx === 0) setSelectedServiceGroupId(serviceGroup.serviceGroupId);

          return {
            serviceGroupName: serviceGroup.serviceGroupName,
            serviceGroupId: serviceGroup.serviceGroupId,
            childs: serviceGroup.childs,
          };
        })
      );
    }
  }, [serviceListData]);

  // 서비스 그룹 선택
  const handleServiceGroupSelected = (event) => {
    setSelectedServiceGroupId(event.data.serviceGroupId);
    setServiceHeaderName(event.data.serviceGroupName);
  };

  // 서비스 데이터
  useEffect(() => {
    if (selectedServiceGroupId) {
      const selectedServiceGroup = serviceGroupList.find(
        (serviceGroup) => serviceGroup.serviceGroupId === selectedServiceGroupId
      );

      if (selectedServiceGroup.childs.length > 0) {
        setServiceList(
          selectedServiceGroup.childs.map((service) => {
            return {
              serviceName: service.name,
              serviceId: service.id,
            };
          })
        );
      }
    }
  }, [selectedServiceGroupId]);

  /**
   * 서비스 전체 선택됨 > 서비스 그룹 id, 하위 서비스 전체 id 전송
   * 서비스 일부 선택됨 > 서비스 그룹 id 전송 x
   * 서비스 그룹 선택 > 서비스 그룹id랑 하위 서비스 전체 id 전송
   */

  // 서비스 그룹 체크
  const handleServiceGroupRowChecked = (event) => {
    const api = event.api;
    const selectedCount = api.getSelectedNodes().length;
    const totalCount = api.getDisplayedRowCount();
    const isSelected = event.node.isSelected();
    const serviceGroupId = event.node.data.serviceGroupId;
    const isClickedServiceGroup = event.event?.target;

    // 전체 선택
    if (totalCount > 0 && selectedCount === totalCount) {
      const serviceGroupIdList = serviceGroupList.map((service) => service.serviceGroupId);
      const serviceIdList = serviceGroupList.flatMap((serviceGroup) => serviceGroup.childs.map((child) => child.id));

      setServiceGroupIds(Array.from(new Set(serviceGroupIdList)));
      setServiceIds(serviceIdList);
    }
    // 전체 해제
    else if (selectedCount === 0) {
      if (event.source === 'uiSelectAll') {
        setServiceGroupIds([]);
        setServiceIds([]);
      }
    }
    // 일부 선택/해제
    else {
      const serviceIdList = serviceGroupList
        .find((serviceGroup) => serviceGroup.serviceGroupId === serviceGroupId)
        ?.childs.map((service) => service.id);

      // 선택
      if (isSelected) {
        setServiceGroupIds((prev) => Array.from(new Set([...prev, serviceGroupId])));
        setServiceIds((prev) => Array.from(new Set([...prev, ...serviceIdList])));
      } else {
        setServiceGroupIds((prev) => prev.filter((prevServiceGroupId) => prevServiceGroupId !== serviceGroupId));

        if (isClickedServiceGroup) {
          setServiceIds((prev) => prev.filter((id) => !serviceIdList.includes(id)));
        }
      }
    }
  };

  // 서비스 체크
  const handleServiceRowChecked = (event) => {
    const { api } = event;
    const selectedCount = api.getSelectedNodes().length;
    const totalCount = api.getDisplayedRowCount();
    const isSelected = event.node.isSelected();
    const serviceId = event.node.data.serviceId;

    // 전체 선택
    if (totalCount > 0 && selectedCount === totalCount) {
      const serviceIdList = serviceList.map((service) => service.serviceId);

      setServiceGroupIds((prev) => Array.from(new Set([...prev, selectedServiceGroupId])));
      setServiceIds((prev) => Array.from(new Set([...prev, ...serviceIdList])));
    }
    // 선택/해제
    else {
      // 선택
      if (isSelected) {
        setServiceIds((prev) => Array.from(new Set([...prev, serviceId])));
      }
      // 선택 해제
      else {
        setServiceIds((prev) => prev.filter((prevServiceId) => prevServiceId !== serviceId));
        setServiceGroupIds((prev) =>
          prev.filter((prevServiceGroupId) => prevServiceGroupId !== selectedServiceGroupId)
        );
      }
    }
  };

  useEffect(() => {
    checkedRows(serviceGroupApi, serviceGroupIds, 'serviceGroupId');
  }, [serviceGroupList, serviceGroupIds]);

  useEffect(() => {
    checkedRows(serviceApi, serviceIds, 'serviceId');
  }, [serviceList, serviceIds]);

  return (
    <div className="service_auth_contents">
      <Grid
        ref={serviceGroupApi}
        placeholder="조회된 서비스 그룹이 없습니다."
        columns={[{ headerName: '서비스 그룹', field: 'serviceGroupName' }]}
        rowData={serviceGroupList}
        autoSizeStrategy={'onGridSizeChanged'}
        defaultColDef={{ onCellClicked: handleServiceGroupSelected }}
        gridOptions={{
          onGridReady: (params) => {
            serviceGroupApi.current = params.api;

            checkedRows(serviceGroupApi, serviceGroupIds, 'serviceGroupId');
          },
          onRowSelected: handleServiceGroupRowChecked,
          rowSelection: {
            checkboxes: true,
            headerCheckbox: true,
            mode: 'multiRow',
          },
          getRowClass: (params) => {
            if (params.data.serviceGroupId === selectedServiceGroupId) return 'selected';
            return '';
          },
        }}
      />
      <Grid
        ref={serviceApi}
        placeholder="조회된 서비스가 없습니다."
        columns={[{ headerName: `${serviceHeaderName} 서비스 리스트`, field: 'serviceName' }]}
        rowData={serviceList}
        autoSizeStrategy={'onGridSizeChanged'}
        gridOptions={{
          onGridReady: (params) => {
            serviceApi.current = params.api;

            checkedRows(serviceApi, serviceIds, 'serviceId');
          },
          onRowSelected: handleServiceRowChecked,
          rowSelection: {
            checkboxes: true,
            headerCheckbox: true,
            mode: 'multiRow',
          },
        }}
      />
    </div>
  );
};

export default ServiceAuthList;
