import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ColDef, ColGroupDef } from '@ag-grid-community/core';
import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import { AgGridReact } from '@ag-grid-community/react';
import { useNavigate } from 'react-router-dom';
import type { PaginationInfo } from '@type/pagination';
import { getGeneralBoardList } from '@api/admin/dynamicBoardApi';
import type { Menu } from './type';
import Grid from '@components/common/Grid/Grid';
import Pagination from '@components/common/Pagination';
import { pageSizeOptions } from '@constants/options';
import DataGrid from '@components/common/Grid/DataGrid';
import SelectBox from '@components/common/SelectBox/SelectBox';

const GeneralBoardList = (menuInfo: Menu) => {
  const navigate = useNavigate();
  const gridRef = useRef<AgGridReact>(null);
  const [column] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: 'no', headerName: 'No.', width: 100, maxWidth: 100, cellClass: 'text-center' },
    { field: 'title', headerName: '제목' },
    { field: 'dateInfo.createUser', headerName: '등록자', cellClass: 'text-center' },
    { field: 'dateInfo.createDate', headerName: '등록일자', cellClass: 'text-center' },
    { field: 'dateInfo.updateUser', headerName: '수정자', cellClass: 'text-center' },
    { field: 'dateInfo.updateDate', headerName: '수정일자', cellClass: 'text-center' },
  ]);

  const [rowData, setRowData] = useState([]);

  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
  });

  const formMethods = useForm<any>({
    defaultValues: {
      title: '',
    },
  });

  // 검색
  const fetchData = async () => {
    const data = await getGeneralBoardList({
      boardTempletId: menuInfo.boardTempletId,
      page: pagination.currentPage - 1,
      size: selectedPageSize.value,
      title: formMethods.getValues('title'),
    });

    setRowData(utils.addSeqNo(data.data, data.pageInfo));
    setPagination((prev) => ({
      ...prev,
      totalCount: data.pageInfo.totalCount,
    }));
  };

  const [selectedPageSize, setSelectedPageSize] = useState<{ label: string; value: number }>(pageSizeOptions[0]);
  useEffect(() => {
    fetchData();
  }, [pagination.currentPage, selectedPageSize]);

  // 상세 페이지 이동
  const onNavigateDetail = (event) => {
    const postId = event.data.id;
    navigate(`${menuInfo.url}/detail/${postId}`);
  };

  //페이지네이션
  const onChangePagination = (key: 'currentPage' | 'pageSize', value: number) => {
    setPagination((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <div className="admin_board">
      <div className="manage_admin_board">
        <Form onSubmit={fetchData} methods={formMethods}>
          <ControlBox>
            <FormInput name="title" label="제목" />
            <Button type="submit" text="조회" />
          </ControlBox>
        </Form>
        <div className="admin_board_contents_control">
          <div className="flex space-x-2">
            <p>총 {pagination.totalCount}건</p>
            <SelectBox
              options={pageSizeOptions}
              defaultValue={selectedPageSize}
              selectedValue={selectedPageSize}
              setSelectedValue={setSelectedPageSize}
            />
          </div>
          <div className="admin_board_contents_control_btn">
            <Button
              text="추가"
              onClick={() => {
                navigate(`${menuInfo.url}/add`);
              }}
            />
          </div>
        </div>
        <div className="admin_board_contents">
          {/* <DataGrid
            ref={gridRef}
            rowEditMode={false}
            addButtonCallback={() => {
              navigate(`${menuInfo.url}/add`);
            }}
            serverPagination={pagination}
            columnDefs={column}
            rowData={rowData}
            getRowClass={() => 'cursor-pointer'}
            defaultColDef={{
              onCellClicked: onNavigateDetail,
            }}
            onPageChange={(currentPage) => onChangePagination('currentPage', currentPage)}
            onPageSizeChange={(pageSize) => onChangePagination('pageSize', pageSize)}
          /> */}
          <Grid
            ref={gridRef}
            columns={column}
            rowData={rowData}
            autoSizeStrategy={'onGridSizeChanged'}
            defaultColDef={{
              onCellClicked: onNavigateDetail,
            }}
          />
          <Pagination
            pageCount={10}
            totalCount={pagination.totalCount}
            currentPage={pagination.currentPage}
            itemCountPerPage={pageSizeOptions[0].value}
            onPageChange={(currentPage) => onChangePagination('currentPage', currentPage)}
          />
        </div>
      </div>
    </div>
  );
};

export default GeneralBoardList;
