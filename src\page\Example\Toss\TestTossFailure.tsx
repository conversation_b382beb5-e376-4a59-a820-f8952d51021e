import Button from '@components/common/Button/Button';
import Title from '@components/common/Title/Title.tsx';
import { useNavigate, useSearchParams } from 'react-router-dom';

const TestTossFailure = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const code = searchParams.get('code');
  const message = searchParams.get('message');

  return (
    <div>
      <div>
        <Button text="주문하러 가기" onClick={() => navigate('/toss/checkout')} />
        <br />
        <br />
      </div>
      <h1 className="c_title">
        <p className="c_title_text">토스 결제에 실패하였습니다.</p>
      </h1>

      <div>
        code: {code}
        <br />
        message: {message}
      </div>
    </div>
  );
};

export default TestTossFailure;
