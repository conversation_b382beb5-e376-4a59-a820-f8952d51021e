import TestTable from '@page/TestPage/TestTable/TestTable';
import Button from '@components/common/Button/Button.tsx';
import IconButton from '@components/common/Button/IconButton';
import TestFilledButtonComp from './TestFilledButtonComp';
import TestUnfilledButtonComp from './TestUnfilledButtonComp';
import TestOutlinedButtonComp from './TestOutlinedButtonComp';

const TestButtonCompWrapper = () => {
  return (
    <>
      <TestTable
        compName="designed_button"
        headChild={
          <>
            <tr>
              <th colSpan={5}>Design Style</th>
            </tr>
            <tr>
              <th>default</th>
              <th>capsule</th>
              <th>rect</th>
              <th>circle</th>
              <th>handle</th>
            </tr>
          </>
        }
        bodyChild={
          <tr className="p_default">
            <td>
              <Button text="default" onClick={() => {}} />
            </td>
            <td>
              <Button text="capsule" design="capsule" onClick={() => {}} />
            </td>
            <td>
              <Button text="rect" design="rect" onClick={() => {}} />
            </td>
            <td>
              <div className="example_wrapper">
                <IconButton text="circle" icon="arrow_left" iconOnly design="circle" onClick={() => {}} />
                <Button text="C" design="circle" onClick={() => {}} />
              </div>
            </td>
            <td>
              {/* <IconButton text="handle" icon= design="handle" onClick={() => {}} /> */}
              개발예정
            </td>
          </tr>
        }
      />
      <TestFilledButtonComp />
      <TestUnfilledButtonComp />
      <TestOutlinedButtonComp />
    </>
  );
};

TestButtonCompWrapper.propTypes = {};

export default TestButtonCompWrapper;
