import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import TestTable from '@page/TestPage/TestTable/TestTable';

const TestFilledButtonComp = () => {
  return (
    <TestTable
      compName="filled_button"
      headChild={
        <>
          <tr>
            <th colSpan={5}>Filled Style</th>
          </tr>
          <tr>
            <th>버튼 색상</th>
            <th>버튼 상태</th>
            <th>아이콘 X, 글자 O</th>
            <th>아이콘 O, 글자 O</th>
            <th>아이콘 O, 글자 X</th>
          </tr>
        </>
      }
      bodyChild={
        <>
          <tr className="p_default">
            <th rowSpan={3}>Primary</th>
            <th>default</th>
            <td>
              <Button text="primary_default" onClick={() => {}} />
            </td>
            <td>
              <IconButton text="primary_default" icon="arrow_left" onClick={() => {}} />
            </td>
            <td>
              <IconButton text="primary_default" icon="arrow_left" iconOnly onClick={() => {}} />
            </td>
          </tr>
          <tr className="p_active">
            <th>active</th>
            <td>
              <Button text="primary_active" className="active" onClick={() => {}} />
            </td>
            <td>
              <IconButton text="primary_active" icon="arrow_left" className="active" onClick={() => {}} />
            </td>
            <td>
              <IconButton text="primary_active" icon="arrow_left" iconOnly className="active" onClick={() => {}} />
            </td>
          </tr>
          <tr className="p_disabled">
            <th>disabled</th>
            <td>
              <Button text="primary_disabled" disabled={true} onClick={() => {}} />
            </td>
            <td>
              <IconButton text="primary_disabled" icon="arrow_left" disabled onClick={() => {}} />
            </td>
            <td>
              <IconButton text="primary_disabled" icon="arrow_left" iconOnly disabled={true} onClick={() => {}} />
            </td>
          </tr>
          <tr className="g_default">
            <th rowSpan={3}>Grayscale</th>
            <th>default</th>
            <td>
              <Button text="grayscale_default" color="grayscale" onClick={() => {}} />
            </td>
            <td>
              <IconButton text="grayscale_default" icon="arrow_left" color="grayscale" onClick={() => {}} />
            </td>
            <td>
              <IconButton text="grayscale_default" icon="arrow_left" iconOnly color="grayscale" onClick={() => {}} />
            </td>
          </tr>
          <tr className="g_active">
            <th>active</th>
            <td>
              <Button text="grayscale_active" color="grayscale" className="active" onClick={() => {}} />
            </td>
            <td>
              <IconButton
                text="grayscale_active"
                icon="arrow_left"
                color="grayscale"
                className="active"
                onClick={() => {}}
              />
            </td>
            <td>
              <IconButton
                text="grayscale_active"
                icon="arrow_left"
                iconOnly
                color="grayscale"
                className="active"
                onClick={() => {}}
              />
            </td>
          </tr>
          <tr className="g_disabled">
            <th>disabled</th>
            <td>
              <Button text="grayscale_disabled" color="grayscale" disabled={true} onClick={() => {}} />
            </td>
            <td>
              <IconButton
                text="grayscale_disabled"
                icon="arrow_left"
                color="grayscale"
                disabled={true}
                onClick={() => {}}
              />
            </td>
            <td>
              <IconButton
                text="grayscale_disabled"
                icon="arrow_left"
                iconOnly
                color="grayscale"
                disabled={true}
                onClick={() => {}}
              />
            </td>
          </tr>
        </>
      }
    />
  );
};

export default TestFilledButtonComp;
