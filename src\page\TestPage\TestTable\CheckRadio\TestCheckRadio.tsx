import { useState } from 'react';
import Checkbox from '@components/common/Input/Checkbox';
import TestTable from '@page/TestPage/TestTable/TestTable';

const TestCheckRadio = () => {
  const [isChecked, setIsChecked] = useState(false);

  return (
    <>
      <TestTable
        compName="checkbox"
        headChild={
          <>
            <tr>
              <th colSpan={5}>Checkbox</th>
            </tr>
            <tr>
              <th>Status</th>
              <th>Default</th>
              <th>Checked</th>
              <th>Disabled</th>
              <th>Checked Disabled</th>
            </tr>
          </>
        }
        bodyChild={
          <>
            <tr>
              <th>no Label (Default)</th>
              <td>
                <Checkbox
                  label="Label"
                  value="11"
                  onChange={(e) => {
                    setIsChecked(e.target.checked);
                  }}
                  checked={isChecked}
                />
              </td>
              <td>
                <Checkbox label="Label" value="11" onChange={() => {}} checked />
              </td>
              <td>
                <Checkbox label="Label" value="" onChange={() => {}} disabled />
              </td>
              <td>
                <Checkbox label="Label" value="" onChange={() => {}} checked disabled />
              </td>
            </tr>
            <tr>
              <th>has Label</th>
              <td>
                <Checkbox
                  label="Label"
                  hasLabel
                  value="11"
                  onChange={(e) => {
                    setIsChecked(e.target.checked);
                  }}
                  checked={isChecked}
                />
              </td>
              <td>
                <Checkbox label="Label" hasLabel value="11" onChange={() => {}} checked />
              </td>
              <td>
                <Checkbox label="Label" hasLabel value="" onChange={() => {}} disabled />
              </td>
              <td>
                <Checkbox label="Label" hasLabel value="" onChange={() => {}} checked disabled />
              </td>
            </tr>
          </>
        }
      />
      <TestTable
        compName="Radio"
        headChild={
          <>
            <tr>
              <th colSpan={5}>Radio</th>
            </tr>
            <tr>
              <th>Status</th>
              <th>Default</th>
              <th>Checked</th>
              <th>Disabled</th>
              <th>Checked Disabled</th>
            </tr>
          </>
        }
        bodyChild={
          <>
            <tr>
              <th>no Label (Default)</th>
              <td>
                <label className="c_radio" htmlFor="example_radio1">
                  <input type="radio" value="1" name="example_radio1" checked={false} onChange={() => {}} />
                  <span className="c_radio_text sr-only">Label</span>
                </label>
              </td>
              <td>
                <label className="c_radio" htmlFor="example_radio2">
                  <input type="radio" value="2" name="example_radio2" checked={true} onChange={() => {}} />
                  <span className="c_radio_text sr-only">Label</span>
                </label>
              </td>
              <td>
                <label className="c_radio" htmlFor="example_radio3">
                  <input type="radio" value="3" name="example_radio3" checked={false} onChange={() => {}} disabled />
                  <span className="c_radio_text sr-only">Label</span>
                </label>
              </td>
              <td>
                <label className="c_radio" htmlFor="example_radio4">
                  <input type="radio" value="4" name="example_radio4" checked={true} onChange={() => {}} disabled />
                  <span className="c_radio_text sr-only">Label</span>
                </label>
              </td>
            </tr>
            <tr>
              <th>has Label</th>
              <td>
                <label className="c_radio" htmlFor="example_radio1">
                  <input type="radio" value="1" name="example_radio1" checked={false} onChange={() => {}} />
                  <span className="c_radio_text">Label</span>
                </label>
              </td>
              <td>
                <label className="c_radio" htmlFor="example_radio2-1">
                  <input type="radio" value="2" name="example_radio2-1" checked={true} onChange={() => {}} />
                  <span className="c_radio_text">Label</span>
                </label>
              </td>
              <td>
                <label className="c_radio" htmlFor="example_radio3">
                  <input type="radio" value="3" name="example_radio3" checked={false} onChange={() => {}} disabled />
                  <span className="c_radio_text">Label</span>
                </label>
              </td>
              <td>
                <label className="c_radio" htmlFor="example_radio4-1">
                  <input type="radio" value="4" name="example_radio4-1" checked={true} onChange={() => {}} disabled />
                  <span className="c_radio_text">Label</span>
                </label>
              </td>
            </tr>
          </>
        }
      />
    </>
  );
};

export default TestCheckRadio;
