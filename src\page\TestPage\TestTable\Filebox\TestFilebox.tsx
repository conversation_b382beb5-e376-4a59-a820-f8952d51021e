import { useState } from 'react';
import TestTable from '../TestTable';
import Filebox from '@components/common/Filebox/Filebox';

const TestFilebox = () => {
  const [singleFile, setSingleFile] = useState<FileList | null>(null);
  const [multipleFile, setMultipleFile] = useState<FileList | null>(null);

  return (
    <TestTable
      compName="Filebox"
      headChild={
        <tr>
          <th colSpan={2}>
            Filebox <br />
            ( 이미지를 클릭하시면 imagePreviewer 모달이 열립니다. )
          </th>
        </tr>
      }
      bodyChild={
        <>
          <tr>
            <th>Single File Box(Default)</th>
            <td>
              <Filebox
                files={singleFile}
                setFiles={setSingleFile}
                accept={['jpg', 'jpeg', 'png', 'gif', 'pdf', 'pptx']}
                className="c_filebox_single"
              />
            </td>
          </tr>
          <tr>
            <th>Multi File Box</th>
            <td>
              <Filebox
                files={multipleFile}
                setFiles={setMultipleFile}
                accept={['jpg', 'jpeg', 'png', 'gif', 'pdf', 'pptx']}
                multiple
                className="c_filebox_multi"
              />
            </td>
          </tr>
        </>
      }
    />
  );
};

export default TestFilebox;
