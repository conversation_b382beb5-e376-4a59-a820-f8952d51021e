import React, { useState } from 'react';
import TestTable from '../TestTable';
import Button from '@components/common/Button/Button';
import Confirm from '@components/common/Modal/Confirm';
import Alert from '@components/common/Modal/Alert';

const TestModal = () => {
  const [confirmA, setIsConfirmA] = useState(false);
  const [confirmB, setIsConfirmB] = useState(false);
  const [alertA, setAlertA] = useState(false);
  const [alertB, setAlertB] = useState(false);

  return (
    <TestTable
      compName="pagination"
      headChild={
        <>
          <tr></tr>
          <tr>
            <th>Status</th>
            <th>Confirm</th>
            <th>Alert</th>
          </tr>
        </>
      }
      bodyChild={
        <>
          <tr>
            <th>No Title (Default)</th>
            <td>
              <div className="c_modal confirm test_modal">
                <div className="c_modal_header"></div>
                <div className="c_modal_body">Content</div>
                <div className="c_modal_footer">
                  <Button color="grayscale" text="닫기" onClick={() => {}} />
                  <Button text="확인" onClick={() => {}} />
                </div>
              </div>

              <Button
                text="타이틀 X, Dim O"
                onClick={() => {
                  setIsConfirmA(true);
                }}
              />
              <Confirm
                isOpenConfirm={confirmA}
                children="타이틀 없는 confirm test"
                onLeftButton={() => {
                  setIsConfirmA(false);
                }}
                onRightButton={() => {
                  console.log('확인');
                }}
              />
            </td>
            <td>
              <div className="c_modal alert test_modal">
                <div className="c_modal_header"></div>
                <div className="c_modal_body">Content</div>
                <div className="c_modal_footer">
                  <Button text="닫기" onClick={() => {}} />
                </div>
              </div>
              <Button
                text="타이틀 X, Dim O"
                onClick={() => {
                  setAlertA(true);
                }}
              />
              <Alert isOpenAlert={alertA} children="타이틀없는 얼럿 테스트" onConfirm={() => setAlertA(false)} />
            </td>
          </tr>
          <tr>
            <th>Has Title</th>
            <td>
              <div className="c_modal confirm test_modal">
                <div className="c_modal_header">
                  <h2 className="c_modal_title">Title</h2>
                </div>
                <div className="c_modal_body">Content</div>
                <div className="c_modal_footer">
                  <Button color="grayscale" text="닫기" onClick={() => {}} />
                  <Button text="확인" onClick={() => {}} />
                </div>
              </div>

              <Button
                text="타이틀 O, Dim X"
                onClick={() => {
                  setIsConfirmB(true);
                }}
              />
              <Confirm
                isOpenConfirm={confirmB}
                title="타이틀 있음"
                children="dim 투명"
                onLeftButton={() => {
                  setIsConfirmB(false);
                }}
                onRightButton={() => {
                  console.log('확인');
                }}
                transparentDim
              />
            </td>
            <td>
              <div className="c_modal alert test_modal">
                <div className="c_modal_header">
                  <h2 className="c_modal_title">Title</h2>
                </div>
                <div className="c_modal_body">Content</div>
                <div className="c_modal_footer">
                  <Button text="닫기" onClick={() => {}} />
                </div>
              </div>
              <Button
                text="타이틀 O, Dim X"
                onClick={() => {
                  setAlertB(true);
                }}
              />
              <Alert
                isOpenAlert={alertB}
                title="타이틀 있음"
                children="dim 투명"
                transparentDim
                onConfirm={() => setAlertB(false)}
              />
            </td>
          </tr>
        </>
      }
    />
  );
};

export default TestModal;
