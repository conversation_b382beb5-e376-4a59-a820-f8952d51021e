import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';

interface TestTableProps {
  compName: string;
  headChild: React.ReactNode;
  bodyChild: React.ReactNode;
}

const TestTable = ({ compName, headChild, bodyChild }: TestTableProps) => {
  return (
    <TableContainer>
      <TableHeader>
        {headChild}
      </TableHeader>
      <TableBody>
        {bodyChild}
      </TableBody>
    </TableContainer>
  );
};

export default TestTable;
