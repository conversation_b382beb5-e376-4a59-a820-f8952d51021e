import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { getVOCCategoryOptions, getVOCList, getVOCStatusOptions } from '@api/admin/vocAPI';
import { useVOCStore } from './store/useVOCStore';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import Pagination from '@components/common/Pagination';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import SelectBox, { OptionType } from '@components/common/SelectBox/SelectBox';
import { useNavigate } from 'react-router-dom';
import VOCAddModal from './components/VOCAddModal';
import Grid from '@components/common/Grid/Grid';
import { RowClickedEvent, RowSelectedEvent } from '@ag-grid-community/core';
import { parseSelectOptions } from '@page/VOC/hooks/useVOC';

export interface searchParamsType {
  page: number;
  size: number;
  isSearch: boolean;
}

const VOCList = () => {
  const navigate = useNavigate();
  const {
    VOCList,
    setVOCList,
    categoryOptions,
    setCategoryOptions,
    statusOptions,
    setStatusOptions,
    VOCPageInfo,
    setVOCPageInfo,
    setIsOpenVOCAddModal,
    isLoading,
  } = useVOCStore();

  const methods = useForm({
    defaultValues: {
      status: '',
      category: '',
      title: '',
    },
  });

  const [searchParams, setSearchParams] = useState<searchParamsType>({
    page: 1,
    size: 20,
    isSearch: false,
    // 검색이 활성화 되어 methods 파라미터 추가되면, isSearch: true로 변경
  });

  const getVOCListData = async () => {
    const { data, pageInfo } = await getVOCList({ data: { ...searchParams, page: searchParams.page - 1 } });
    setVOCList(data);
    setVOCPageInfo(pageInfo);
  };

  const getVOCCategoryOptionsData = async () => {
    const data = await getVOCCategoryOptions({});
    setCategoryOptions(parseSelectOptions({ data, hasAll: true }));
  };

  const getVOCStatusOptionsData = async () => {
    const data = await getVOCStatusOptions({});
    setStatusOptions(parseSelectOptions({ data, hasAll: true }));
  };

  // 상태 및 카테고리 옵션 맵핑
  useEffect(() => {
    getVOCCategoryOptionsData();
    getVOCStatusOptionsData();
  }, []);

  // VOC 목록 조회, 검색파라미터 변경시 호출
  useEffect(() => {
    console.log('searchParams', searchParams);
    getVOCListData();
  }, [searchParams]);

  const colDefs = [
    { headerName: 'ID', field: 'id', width: 90, sortable: true, unSortIcon: true },
    {
      headerName: '상태',
      field: 'status',
      width: 120,
      cellRenderer: (params: any) => <span className={`voc_status_card ${params.value}`}>{params.value}</span>,
    },
    { headerName: '카테고리', field: 'category', flex: 1 },
    { headerName: '제목', field: 'title', flex: 1 },
    { headerName: '조회수', field: 'viewCnt', width: 90 },
    { headerName: '등록자', field: 'dateInfo.createUser', width: 90 },
    { headerName: '등록일시', field: 'dateInfo.createDate', width: 170 },
    { headerName: '수정자', field: 'dateInfo.updateUser', width: 90 },
    { headerName: '수정일시', field: 'dateInfo.updateDate', width: 170 },
  ];

  const handleSubmit = (data: any) => {
    setSearchParams((prev) => {
      const parseData = {
        ...(data.category && { category: data?.category?.value }),
        ...(data.status && { status: data?.status?.value }),
        title: data.title || '',
        isSearch: true,
      };
      return { ...prev, ...parseData };
    });
  };

  const onRowClicked = (event: RowClickedEvent<any, any>) => {
    navigate(`/voc/detail/${event.data.id}`);
  };

  const handlePageChange = (pageNo: number) => {
    setSearchParams((prev) => ({ ...prev, page: pageNo }));
  };

  const handleClickAdd = () => {
    setIsOpenVOCAddModal(true);
  };

  const handleSizeChange = (size: OptionType) => {
    setSearchParams((prev) => ({ ...prev, page: 1, size: Number(size.value) }));
  };

  return (
    <div className="voc">
      <div className="voc_list">
        <Form methods={methods} onSubmit={handleSubmit}>
          <ControlBox>
            <label className="c_controller_label">상태</label>
            <FormSelectBox name="status" options={statusOptions} placeholder="상태" />
            <label className="c_controller_label">카테고리</label>
            <FormSelectBox name="category" options={categoryOptions} placeholder="카테고리" />
            <label className="c_controller_label">VOC 키워드</label>
            <FormInput name="title" placeholder="VOC 키워드 입력" />
            <Button type="submit" text="검색" disabled={isLoading} />
          </ControlBox>
        </Form>
        <TableContainerHeader
          leftChildren={
            <>
              <SelectBox
                options={[
                  { label: '10', value: 10 },
                  { label: '20', value: 20 },
                  { label: '30', value: 30 },
                ]}
                defaultValue={{ label: searchParams.size.toString(), value: searchParams.size }}
                selectedValue={{ label: searchParams.size.toString(), value: searchParams.size }}
                setSelectedValue={handleSizeChange}
                disabled={isLoading}
              />
              <p className="font-bold">총 {VOCPageInfo?.totalCount}건</p>
            </>
          }
          rightChildren={<Button text="VOC 등록" onClick={handleClickAdd} />}
        />

        <Grid
          columns={colDefs}
          rowData={VOCList}
          autoSizeStrategy={'onGridSizeChanged'}
          gridOptions={{
            onRowClicked: onRowClicked,
          }}
        />
        {VOCPageInfo && (
          <Pagination
            pageCount={VOCPageInfo.totalPages}
            totalCount={VOCPageInfo.totalCount}
            itemCountPerPage={searchParams.size}
            currentPage={searchParams.page}
            onPageChange={handlePageChange}
            disabled={isLoading}
          />
        )}
      </div>
      <VOCAddModal getVOCListData={getVOCListData} />
    </div>
  );
};

export default VOCList;
