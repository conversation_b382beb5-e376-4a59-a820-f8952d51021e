import { useEffect, useState } from 'react';
import { Outlet, useLocation, useMatches, useNavigate } from 'react-router-dom';
import { useLayoutStore } from '@store/useLayoutStore';
import { useMenuStore } from '@store/useMenuStore';
import Header from '@components/layout/Header';
import Main from '@components/layout/Main';
import Footer from '@components/layout/Footer';
import Loading from '@components/common/Loading';
import { getMyMenus } from '@api/admin/menuManageApi';
import Error from '@page/Error';
import { Menu } from 'types/adminMenuType';
import { useLogger } from '@components/common/Log/hooks/useLogger';
import { useLogStore } from '@store/useLogStore';
import { createTraceId } from '@components/common/Log/utils/createTraceId';
import { getFrontInfo } from '@components/common/Log/utils/getFrontInfo';
import { useLoadingStore } from '@store/useLoadingStore';

const ProtectedRoute = () => {
  // hooks
  const location = useLocation();
  const navigate = useNavigate();
  const { myMenuList } = useMenuStore();
  const { isLoading } = useLoadingStore();

  // state
  const { lnbState } = useLayoutStore();

  // 스크린 로그 관련
  const { screenLog } = useLogger();
  const { setLogParams } = useLogStore();
  const matches = useMatches();
  const lastMatch = matches[matches.length - 1];
  const path = lastMatch?.pathname;

  useEffect(() => {
    if (myMenuList.length !== 0) {
      if (location.pathname === '/') {
        // navigate(myMenuList[0].url);
        navigate('/myInfo');
      }
    }
  }, [myMenuList]);

  /**
   * 스크린 로그
   */

  const findMenuByUrl = (menuList: Menu[], currentUrl: String) => {
    for (const menu of menuList) {
      if (menu.url === currentUrl) {
        return menu;
      }
      if (menu.childMenus) {
        const found = findMenuByUrl(menu.childMenus, currentUrl);
        if (found) return found;
      }
    }
    return null;
  };

  const handlePageLogging = (usingMenuList: Menu[]) => {
    // menulist가 비어있지 않을 때에만
    if (usingMenuList.length > 0) {
      const currentMenu = findMenuByUrl(usingMenuList, path);
      // const currentPageName = usingMenuList.find((menulist) => path.includes(menulist.url))?.name;

      // 페이지 접근 로그 api 호출
      if (currentMenu) {
        setLogParams((prev) => ({
          ...prev,
          menuName: currentMenu.name,
          buttonSection: '',
          button: '',
          logType: 'screen',
          globalTraceId: createTraceId(),
          ...getFrontInfo(),
        }));
        screenLog();
      }
    }
  };

  // 페이지 이동 시 스크린 로그 호출
  useEffect(() => {
    if (!path.endsWith('/')) {
      handlePageLogging(myMenuList);
    }
  }, [path, myMenuList]);

  // 정상 라우팅 처리
  return (
    <div data-is-open-lnb={lnbState}>
      <Header />
      <Main>
        <Outlet />
      </Main>
      <Footer />
    </div>
  );
};

export default ProtectedRoute;
