import { createBrowserRouter, Navigate, RouterProvider, useLocation, useNavigate } from 'react-router-dom';
import { createElement, Suspense, useEffect, useMemo, useState } from 'react';
import { useMenuStore } from '@store/useMenuStore';
import Login from '@page/Login/Login';
import Error from '@page/Error';
import MyInfo from '@page/MyInfo/MyInfo';
import App from 'App';
import ProtectedRoute from 'route/ProtectedRoute';
import { lazy } from 'react';
import { getMyMenus } from '@api/admin/menuManageApi';
import Loading from '@components/common/Loading';
import { useLoadingStore } from '@store/useLoadingStore';
import { Menu } from '@type/adminMenuType';
import { useLoginStore } from '@store/useLoginStore';
import Logout from '@page/Login/Logout';

export const RoutingDev = () => {
  const { myMenuList, setMyMenuList } = useMenuStore();
  const [isMenuLoaded, setIsMenuLoaded] = useState(false);
  const [dynamicMenuList, setDynamicMenuList] = useState([]);
  const { isLogin, setIsLogin } = useLoginStore();
  const location = useLocation();

  // 본인 권한 메뉴 조회 목록 >> 최초 권한 조회
  const getMyMenusData = async () => {
    setIsMenuLoaded(false);
    const response = await getMyMenus({});
    // 메뉴목록이 없으면 추가를 안함
    if (!response) {
      setMyMenuList([]);
      return;
    };
    setMyMenuList(response);
    setIsMenuLoaded(true);
  };

  useEffect(() => {
    // 로그인 페이지가 아닐 때에만 체크
    
  }, []);

  const createMenus = (menuList) => {
    const routes = [];

    const buildRoutes = (menus) => {
      menus.forEach((menu) => {
        // index 처리 
        if (menu.parentId === null && menu.childMenus && menu.childMenus.length > 0) {
          routes.push({
            path: menu.url,
            element: <Navigate to={menu.childMenus[0].url} replace />,
          });
        }

        if (menu.filePath) {
          const ReactComponent = lazy(() => import(`../page${menu.filePath}`));
          routes.push({
            path: menu.url,
            element: createElement(ReactComponent, menu),
          });
        }

        if (menu.childPages) {
          menu.childPages.forEach((page) => {
            const ReactComponent = lazy(() => import(`../page${page.filePath}`));
            let pageUrl = menu.url;
            if (page.pageTypeCode === 'L' || page.pageTypeCode === 'B') {
              pageUrl += '/list';
            } else if (page.pageTypeCode === 'P') {
              pageUrl += '/detail/:id';
            } else if (page.pageTypeCode === 'R') {
              pageUrl += '/add';
            } else if (page.pageTypeCode === 'M') {
              pageUrl += '/edit/:id';
            } else if (page.pageTypeCode === 'T') {
              pageUrl += '/test';
            }
            routes.push({
              path: pageUrl,
              element: createElement(ReactComponent, menu),
            });
            // console.log('하위 페이지 경로 생성:', pageUrl);
          });
        }
        if (menu.childMenus && menu.childMenus.length > 0) {
          buildRoutes(menu.childMenus);
        }
      });
    };

    buildRoutes(menuList);
    return routes;
  };

  useEffect(() => {
    const myInfo = [
      {
        path: 'myInfo',
        element: <MyInfo />,
      },
    ];
    if (myMenuList.length > 0) {
      const dynamicMenus = createMenus(myMenuList);
      setDynamicMenuList([...myInfo, ...dynamicMenus]);
    } else {
      setDynamicMenuList([...myInfo]);
    }
  }, [myMenuList]);

  const dynamicRoute = useMemo(() => {
    return createBrowserRouter([
      {
        element: <App />,
        children: [
          {
            path: '/',
            element: <ProtectedRoute />,
            children: [
              // 동적 메뉴 생성
              {
                index: true,
                element: <Navigate to="/myInfo" replace />,
              },
              ...dynamicMenuList,
            ],
          },
          {
            path: '*',
            element: <Navigate to="/error" replace={true} />,
          },
          {
            path: 'error',
            element: <Error />,
          },
          {
            path: 'login',
            element: <Login />,
          },
          {
            path: 'logout',
            element: <Logout />,
          },
        ],
      },
    ])
  }, [dynamicMenuList]);
  console.log("dynamicMenuList", isLogin, dynamicMenuList, isMenuLoaded)
  if (isLogin && !isMenuLoaded) {
    return (
      <Loading isLoading={true} />
    );
  }
  return <RouterProvider router={dynamicRoute} />;
};
